/* GitHub README Theme - Familiar developer-friendly design */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  --color-canvas-default: #ffffff;
  --color-canvas-subtle: #f6f8fa;
  --color-border-default: #d0d7de;
  --color-border-muted: #d8dee4;
  --color-neutral-muted: rgba(175, 184, 193, 0.2);
  --color-accent-fg: #0969da;
  --color-accent-emphasis: #0969da;
  --color-success-fg: #1a7f37;
  --color-attention-fg: #9a6700;
  --color-severe-fg: #bc4c00;
  --color-danger-fg: #d1242f;
  --color-fg-default: #1f2328;
  --color-fg-muted: #656d76;
  --color-fg-subtle: #6e7781;
  --fontStack-monospace: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--color-fg-default);
  background-color: var(--color-canvas-default);
  margin: 0;
  padding: 0;
  word-wrap: break-word;
}

.container {
  max-width: 1012px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--color-canvas-default);
  min-height: 100vh;
}

/* Repository Header */
.repo-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border-default);
}

.repo-icon {
  color: var(--color-fg-muted);
}

.repo-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-accent-fg);
  text-decoration: none;
}

.repo-visibility {
  font-size: 0.75rem;
  padding: 0.125rem 0.5rem;
  border: 1px solid var(--color-border-default);
  border-radius: 2rem;
  color: var(--color-fg-muted);
  background: var(--color-canvas-subtle);
}

/* Badges */
.badges {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.375rem;
  text-decoration: none;
  border: 1px solid transparent;
}

.badge-build {
  background-color: var(--color-success-fg);
  color: white;
}

.badge-version {
  background-color: var(--color-accent-emphasis);
  color: white;
}

.badge-license {
  background-color: var(--color-attention-fg);
  color: white;
}

.badge-downloads {
  background-color: var(--color-severe-fg);
  color: white;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.25;
  color: var(--color-fg-default);
}

h1 {
  font-size: 2rem;
  border-bottom: 1px solid var(--color-border-muted);
  padding-bottom: 0.3rem;
  margin-top: 0;
}

h2 {
  font-size: 1.5rem;
  border-bottom: 1px solid var(--color-border-muted);
  padding-bottom: 0.3rem;
  margin-top: 2rem;
}

h3 {
  font-size: 1.25rem;
}

h4 {
  font-size: 1rem;
}

h5 {
  font-size: 0.875rem;
}

h6 {
  font-size: 0.85rem;
  color: var(--color-fg-muted);
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Links */
a {
  color: var(--color-accent-fg);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Lists */
ul, ol {
  margin-top: 0;
  margin-bottom: 1rem;
  padding-left: 2rem;
}

li {
  margin: 0.25rem 0;
}

li > p {
  margin-top: 1rem;
}

li + li {
  margin-top: 0.25rem;
}

/* Task Lists */
.task-list-item {
  list-style-type: none;
  margin-left: -2rem;
}

.task-list-item-checkbox {
  margin: 0 0.2rem 0.25rem -1.4rem;
  vertical-align: middle;
}

/* Code */
code, tt {
  padding: 0.2rem 0.4rem;
  margin: 0;
  font-size: 85%;
  white-space: break-spaces;
  background-color: var(--color-neutral-muted);
  border-radius: 6px;
  font-family: var(--fontStack-monospace);
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  padding: 1rem;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: var(--color-canvas-subtle);
  border-radius: 6px;
  border: 1px solid var(--color-border-default);
}

pre code {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

/* Code blocks with language */
.highlight {
  margin-bottom: 1rem;
}

.highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

/* Blockquotes */
blockquote {
  margin: 0 0 1rem 0;
  padding: 0 1rem;
  color: var(--color-fg-muted);
  border-left: 0.25rem solid var(--color-border-default);
}

blockquote > :first-child {
  margin-top: 0;
}

blockquote > :last-child {
  margin-bottom: 0;
}

/* Tables */
table {
  border-spacing: 0;
  border-collapse: collapse;
  display: block;
  width: max-content;
  max-width: 100%;
  overflow: auto;
  margin-top: 0;
  margin-bottom: 1rem;
}

table th {
  font-weight: 600;
}

table th,
table td {
  padding: 6px 13px;
  border: 1px solid var(--color-border-default);
}

table tr {
  background-color: var(--color-canvas-default);
  border-top: 1px solid var(--color-border-muted);
}

table tr:nth-child(2n) {
  background-color: var(--color-canvas-subtle);
}

table img {
  background-color: transparent;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  box-sizing: content-box;
  background-color: var(--color-canvas-default);
}

img[align=right] {
  padding-left: 20px;
}

img[align=left] {
  padding-right: 20px;
}

/* Horizontal Rule */
hr {
  height: 0.25rem;
  padding: 0;
  margin: 1.5rem 0;
  background-color: var(--color-border-default);
  border: 0;
}

/* Alerts/Callouts */
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 1rem;
  border-left: 0.25rem solid var(--color-border-default);
  border-radius: 0 6px 6px 0;
}

.markdown-alert-note {
  border-left-color: var(--color-accent-emphasis);
  background-color: rgba(9, 105, 218, 0.1);
}

.markdown-alert-tip {
  border-left-color: var(--color-success-fg);
  background-color: rgba(26, 127, 55, 0.1);
}

.markdown-alert-important {
  border-left-color: var(--color-severe-fg);
  background-color: rgba(188, 76, 0, 0.1);
}

.markdown-alert-warning {
  border-left-color: var(--color-attention-fg);
  background-color: rgba(154, 103, 0, 0.1);
}

.markdown-alert-caution {
  border-left-color: var(--color-danger-fg);
  background-color: rgba(209, 36, 47, 0.1);
}

.markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1;
  margin-bottom: 0.5rem;
}

/* Installation/Usage sections */
.installation {
  background: var(--color-canvas-subtle);
  border: 1px solid var(--color-border-default);
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
}

.installation h3 {
  margin-top: 0;
  color: var(--color-fg-default);
}

/* Directory structure */
.file-tree {
  font-family: var(--fontStack-monospace);
  font-size: 0.875rem;
  line-height: 1.45;
  background: var(--color-canvas-subtle);
  border: 1px solid var(--color-border-default);
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
}

.file-tree .directory {
  color: var(--color-accent-fg);
  font-weight: 600;
}

.file-tree .file {
  color: var(--color-fg-default);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  table {
    font-size: 0.875rem;
  }
  
  .badges {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .repo-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Print Styles */
@media print {
  .container {
    max-width: none;
    padding: 0;
  }
  
  .badges,
  .repo-header {
    display: none;
  }
  
  a {
    color: var(--color-fg-default);
    text-decoration: none;
  }
  
  pre {
    break-inside: avoid;
  }
}
