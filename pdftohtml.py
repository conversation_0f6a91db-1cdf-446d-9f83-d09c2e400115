# PDF to HTML Conversion in Python
# Multiple approaches for different use cases

import os
from pathlib import Path

# Method 1: Using pdfplumber (Best for text extraction with some formatting)
def pdf_to_html_pdfplumber(pdf_path, output_path):
    """
    Convert PDF to HTML using pdfplumber
    Good for: Text extraction with basic formatting
    """
    try:
        import pdfplumber
    except ImportError:
        print("Install pdfplumber: pip install pdfplumber")
        return
    
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>PDF Content</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            /* Modern CSS Styling for PDF to HTML Conversion */
            * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
            }

            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #2c3e50;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                min-height: 100vh;
                padding: 20px;
                animation: backgroundShift 10s ease-in-out infinite;
            }

            @keyframes backgroundShift {
                0%, 100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
                50% { background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%); }
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
                color: white;
                padding: 30px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
                position: relative;
                overflow: hidden;
            }

            .header::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
                animation: shimmer 3s infinite;
            }

            @keyframes shimmer {
                0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
                100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            }

            .header h1 {
                font-size: 2.5em;
                margin-bottom: 10px;
                font-weight: 300;
            }

            .header p {
                opacity: 0.9;
                font-size: 1.1em;
            }

            .content {
                padding: 40px;
            }

            .page {
                margin-bottom: 60px;
                padding: 30px;
                background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
                border-radius: 15px;
                border: 3px solid transparent;
                background-clip: padding-box;
                box-shadow: 0 15px 35px rgba(255, 107, 107, 0.2);
                position: relative;
                transition: all 0.3s ease;
                overflow: hidden;
            }

            .page::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
                z-index: -1;
                margin: -3px;
                border-radius: 15px;
            }

            .page:hover {
                transform: translateY(-5px) scale(1.02);
                box-shadow: 0 25px 50px rgba(255, 107, 107, 0.3);
            }

            .page:last-child {
                margin-bottom: 0;
            }

            .page-number {
                position: absolute;
                top: -15px;
                right: 20px;
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
                color: white;
                padding: 10px 18px;
                border-radius: 25px;
                font-size: 14px;
                font-weight: 700;
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
                animation: pulse 2s infinite;
                border: 2px solid white;
            }

            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.1); }
            }

            .page p {
                margin-bottom: 15px;
                text-align: justify;
                font-size: 16px;
                line-height: 1.8;
            }

            .page p:last-child {
                margin-bottom: 0;
            }

            /* Typography improvements */
            h1, h2, h3, h4, h5, h6 {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                margin-bottom: 20px;
                font-weight: 700;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            }

            h1 {
                font-size: 2.5em;
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
            h2 {
                font-size: 2em;
                background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
            h3 {
                font-size: 1.7em;
                background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }

            /* Links and interactive elements */
            a {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                text-decoration: none;
                border-bottom: 3px solid transparent;
                transition: all 0.3s ease;
                position: relative;
                padding: 2px 4px;
            }

            a::before {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                width: 0;
                height: 3px;
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
                transition: width 0.3s ease;
            }

            a:hover::before {
                width: 100%;
            }

            a:hover {
                transform: translateY(-2px);
                text-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
            }

            /* Code and preformatted text */
            code, pre {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: 2px solid #4ecdc4;
                border-radius: 10px;
                font-family: 'Courier New', monospace;
                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
            }

            code {
                padding: 4px 8px;
                font-size: 0.9em;
                display: inline-block;
                margin: 2px;
                animation: codeGlow 3s ease-in-out infinite;
            }

            @keyframes codeGlow {
                0%, 100% { box-shadow: 0 0 5px rgba(78, 205, 196, 0.5); }
                50% { box-shadow: 0 0 20px rgba(78, 205, 196, 0.8); }
            }

            pre {
                padding: 20px;
                overflow-x: auto;
                margin: 20px 0;
                position: relative;
            }

            pre::before {
                content: '💻 Code';
                position: absolute;
                top: -10px;
                left: 20px;
                background: #4ecdc4;
                color: white;
                padding: 5px 15px;
                border-radius: 15px;
                font-size: 12px;
                font-weight: 600;
            }

            /* Tables */
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                background: white;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            }

            th, td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #e9ecef;
            }

            th {
                background: #667eea;
                color: white;
                font-weight: 600;
            }

            tr:hover {
                background: #f8f9fa;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }

                .header {
                    padding: 20px;
                }

                .header h1 {
                    font-size: 2em;
                }

                .content {
                    padding: 20px;
                }

                .page {
                    padding: 20px;
                    margin-bottom: 30px;
                }

                .page p {
                    font-size: 14px;
                }
            }

            /* Print styles */
            @media print {
                body {
                    background: white;
                    padding: 0;
                }

                .container {
                    box-shadow: none;
                    border-radius: 0;
                }

                .header {
                    background: #667eea !important;
                    -webkit-print-color-adjust: exact;
                }

                .page {
                    break-inside: avoid;
                    box-shadow: none;
                    border: 1px solid #ddd;
                }

                .page-number {
                    background: #667eea !important;
                    -webkit-print-color-adjust: exact;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📄 PDF Document</h1>
                <p>Converted to HTML with enhanced styling</p>
            </div>
            <div class="content">
    """
    
    with pdfplumber.open(pdf_path) as pdf:
        for i, page in enumerate(pdf.pages):
            text = page.extract_text()
            if text:
                # Convert line breaks to HTML
                text = text.replace('\n\n', '</p><p>')
                text = text.replace('\n', '<br>')
                
                html_content += f"""
                <div class="page">
                    <div class="page-number">Page {i + 1}</div>
                    <p>{text}</p>
                </div>
                """
    
    html_content += """
            </div>
        </div>
    </body>
    </html>"""

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"HTML saved to: {output_path}")

# Method 2: Using PyMuPDF (fitz) - More advanced layout preservation
def pdf_to_html_pymupdf(pdf_path, output_path):
    """
    Convert PDF to HTML using PyMuPDF
    Good for: Better layout and formatting preservation
    """
    try:
        import fitz  # PyMuPDF
    except ImportError:
        print("Install PyMuPDF: pip install PyMuPDF")
        return
    
    doc = fitz.open(pdf_path)
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>PDF Content - Advanced Layout</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            /* Advanced CSS Styling for PyMuPDF Conversion */
            * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
            }

            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #2c3e50;
                background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #fad0c4 100%);
                min-height: 100vh;
                padding: 20px;
                animation: rainbowShift 8s ease-in-out infinite;
            }

            @keyframes rainbowShift {
                0%, 100% { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #fad0c4 100%); }
                25% { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
                50% { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
                75% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 25px 50px rgba(0,0,0,0.15);
                overflow: hidden;
            }

            .header {
                background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
                color: white;
                padding: 30px;
                text-align: center;
                box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
                position: relative;
                overflow: hidden;
            }

            .header::after {
                content: '🌈';
                position: absolute;
                top: 10px;
                right: 30px;
                font-size: 2em;
                animation: bounce 2s infinite;
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                60% { transform: translateY(-5px); }
            }

            .header h1 {
                font-size: 2.5em;
                margin-bottom: 10px;
                font-weight: 300;
            }

            .header p {
                opacity: 0.9;
                font-size: 1.1em;
            }

            .content {
                padding: 40px;
            }

            .page {
                margin-bottom: 50px;
                padding: 35px;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-radius: 12px;
                border-left: 6px solid #667eea;
                box-shadow: 0 8px 25px rgba(0,0,0,0.08);
                position: relative;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .page:hover {
                transform: translateY(-2px);
                box-shadow: 0 12px 35px rgba(0,0,0,0.12);
            }

            .page:last-child {
                margin-bottom: 0;
            }

            .page h3 {
                color: #667eea;
                font-size: 1.8em;
                margin-bottom: 25px;
                padding-bottom: 10px;
                border-bottom: 2px solid #e9ecef;
                position: relative;
            }

            .page h3::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 50px;
                height: 2px;
                background: #667eea;
            }

            .text-block {
                margin-bottom: 20px;
                padding: 15px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                border-left: 3px solid #e9ecef;
                transition: border-left-color 0.3s ease;
            }

            .text-block:hover {
                border-left-color: #667eea;
            }

            .text-block span {
                display: inline-block;
                margin: 2px 0;
                transition: all 0.2s ease;
            }

            /* Enhanced typography for different font sizes */
            .text-block span[style*="font-size: 24px"],
            .text-block span[style*="font-size: 22px"],
            .text-block span[style*="font-size: 20px"] {
                display: block;
                margin: 15px 0;
                color: #2c3e50;
                font-weight: 600;
            }

            .text-block span[style*="font-size: 18px"],
            .text-block span[style*="font-size: 16px"] {
                color: #34495e;
            }

            .text-block span[style*="font-size: 14px"],
            .text-block span[style*="font-size: 12px"] {
                color: #7f8c8d;
                font-size: 0.9em;
            }

            /* Bold and italic enhancements */
            .text-block span[style*="font-weight: bold"] {
                color: #2c3e50;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }

            .text-block span[style*="font-style: italic"] {
                color: #5d6d7e;
            }

            /* Responsive design */
            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }

                .header {
                    padding: 20px;
                }

                .header h1 {
                    font-size: 2em;
                }

                .content {
                    padding: 20px;
                }

                .page {
                    padding: 20px;
                    margin-bottom: 30px;
                }

                .text-block {
                    padding: 10px;
                }
            }

            /* Print styles */
            @media print {
                body {
                    background: white;
                    padding: 0;
                }

                .container {
                    box-shadow: none;
                    border-radius: 0;
                }

                .header {
                    background: #2c3e50 !important;
                    -webkit-print-color-adjust: exact;
                }

                .page {
                    break-inside: avoid;
                    box-shadow: none;
                    border: 1px solid #ddd;
                    background: white !important;
                }

                .text-block {
                    box-shadow: none;
                    background: white !important;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📄 PDF Document</h1>
                <p>Advanced layout preservation with PyMuPDF</p>
            </div>
            <div class="content">
    """
    
    for page_num in range(doc.page_count):
        page = doc[page_num]
        
        # Get text with formatting information
        text_dict = page.get_text("dict")
        
        html_content += f'<div class="page"><h3>Page {page_num + 1}</h3>'
        
        for block in text_dict["blocks"]:
            if "lines" in block:  # Text block
                block_text = ""
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"]
                        font_size = span["size"]
                        font_flags = span["flags"]
                        
                        # Apply formatting based on font properties
                        style = f"font-size: {font_size}px;"
                        if font_flags & 2**4:  # Bold
                            style += " font-weight: bold;"
                        if font_flags & 2**1:  # Italic
                            style += " font-style: italic;"
                        
                        block_text += f'<span style="{style}">{text}</span>'
                    block_text += "<br>"
                
                html_content += f'<div class="text-block">{block_text}</div>'
        
        html_content += '</div>'
    
    html_content += """
            </div>
        </div>
    </body>
    </html>"""
    doc.close()

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"HTML saved to: {output_path}")

# Method 3: Using pdfminer - Low-level control
def pdf_to_html_pdfminer(pdf_path, output_path):
    """
    Convert PDF to HTML using pdfminer
    Good for: Detailed control over extraction process
    """
    try:
        from pdfminer.high_level import extract_text_to_fp
        from pdfminer.layout import LAParams
        from io import StringIO
    except ImportError:
        print("Install pdfminer.six: pip install pdfminer.six")
        return
    
    output_string = StringIO()
    
    with open(pdf_path, 'rb') as file:
        extract_text_to_fp(
            file, 
            output_string,
            laparams=LAParams(),
            output_type='html',
            codec=None
        )
    
    html_content = output_string.getvalue()

    # Enhance the HTML with better CSS styling
    enhanced_css = """
    <style>
        /* Enhanced CSS for PDFMiner HTML Output */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 25%, #a8edea 50%, #fed6e3 75%, #ffecd2 100%);
            min-height: 100vh;
            padding: 20px;
            animation: colorWave 12s ease-in-out infinite;
        }

        @keyframes colorWave {
            0%, 100% { background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 25%, #a8edea 50%, #fed6e3 75%, #ffecd2 100%); }
            20% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            40% { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
            60% { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
            80% { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #feca57 100%);
            color: white;
            padding: 30px;
            text-align: center;
            box-shadow: 0 20px 50px rgba(255, 107, 107, 0.5);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '🎨✨🌟';
            position: absolute;
            top: 50%;
            left: -100%;
            transform: translateY(-50%);
            font-size: 1.5em;
            animation: sparkleMove 4s linear infinite;
        }

        @keyframes sparkleMove {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .content {
            padding: 40px;
        }

        /* Override default PDFMiner styles */
        div {
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #74b9ff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        span {
            line-height: 1.8;
            transition: all 0.2s ease;
        }

        span:hover {
            background: rgba(116, 185, 255, 0.1);
            border-radius: 3px;
            padding: 2px 4px;
        }

        /* Typography enhancements */
        span[style*="font-size:24px"],
        span[style*="font-size:22px"],
        span[style*="font-size:20px"] {
            display: block;
            margin: 15px 0;
            color: #2c3e50;
            font-weight: 600;
        }

        span[style*="font-weight:bold"] {
            color: #2c3e50;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        span[style*="font-style:italic"] {
            color: #636e72;
        }

        /* Links */
        a {
            color: #74b9ff;
            text-decoration: none;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        a:hover {
            border-bottom-color: #74b9ff;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            div {
                padding: 8px;
            }
        }

        /* Print styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }

            .container {
                box-shadow: none;
                border-radius: 0;
            }

            .header {
                background: #00b894 !important;
                -webkit-print-color-adjust: exact;
            }

            div {
                box-shadow: none;
                background: white !important;
                border: 1px solid #ddd;
            }
        }
    </style>
    """

    # Insert enhanced CSS and wrap content
    if '<head>' in html_content:
        html_content = html_content.replace('<head>', f'<head>\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n{enhanced_css}')
    else:
        # If no head tag, add it
        html_content = html_content.replace('<html>', f'<html>\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<title>PDF Content - PDFMiner</title>\n{enhanced_css}\n</head>')

    # Wrap body content in container
    if '<body>' in html_content:
        html_content = html_content.replace('<body>', '''<body>
        <div class="container">
            <div class="header">
                <h1>📄 PDF Document</h1>
                <p>Detailed extraction with PDFMiner</p>
            </div>
            <div class="content">''')
        html_content = html_content.replace('</body>', '''
            </div>
        </div>
    </body>''')

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    print(f"HTML saved to: {output_path}")

# Method 4: Advanced conversion with pdf2htmlEX (requires external tool)
def pdf_to_html_pdf2htmlex(pdf_path, output_dir):
    """
    Convert PDF to HTML using pdf2htmlEX (external tool)
    Best for: Pixel-perfect HTML conversion with CSS
    Note: Requires pdf2htmlEX to be installed on system
    """
    import subprocess
    
    try:
        # Check if pdf2htmlEX is installed
        subprocess.run(["pdf2htmlEX", "--version"], 
                      capture_output=True, check=True)
        
        # Convert PDF
        output_name = Path(pdf_path).stem + ".html"
        subprocess.run([
            "pdf2htmlEX",
            "--zoom", "1.3",
            "--dest-dir", output_dir,
            pdf_path,
            output_name
        ], check=True)
        
        print(f"HTML saved to: {os.path.join(output_dir, output_name)}")
        
    except subprocess.CalledProcessError:
        print("pdf2htmlEX not found. Install it from: https://github.com/coolwanglu/pdf2htmlEX")
    except FileNotFoundError:
        print("pdf2htmlEX not found in PATH")

# Method 5: Batch conversion utility
def batch_convert_pdfs(input_dir, output_dir, method="pdfplumber"):
    """
    Convert multiple PDFs to HTML
    """
    Path(output_dir).mkdir(exist_ok=True)
    
    methods = {
        "pdfplumber": pdf_to_html_pdfplumber,
        "pymupdf": pdf_to_html_pymupdf,
        "pdfminer": pdf_to_html_pdfminer
    }
    
    converter = methods.get(method, pdf_to_html_pdfplumber)
    
    for pdf_file in Path(input_dir).glob("*.pdf"):
        output_file = Path(output_dir) / f"{pdf_file.stem}.html"
        print(f"Converting: {pdf_file.name}")
        converter(str(pdf_file), str(output_file))

# Example usage
if __name__ == "__main__":
    # Check for PDF files in current directory
    pdf_files = list(Path(".").glob("*.pdf"))

    if not pdf_files:
        print("No PDF files found in current directory.")
        pdf_file = input("Enter the path to your PDF file: ").strip()
        if not os.path.exists(pdf_file):
            print(f"Error: File '{pdf_file}' not found!")
            exit(1)
    else:
        print("Found PDF files:")
        for i, pdf in enumerate(pdf_files, 1):
            print(f"{i}. {pdf.name}")

        if len(pdf_files) == 1:
            pdf_file = str(pdf_files[0])
            print(f"Using: {pdf_file}")
        else:
            try:
                choice_num = int(input("Select PDF file number: ")) - 1
                pdf_file = str(pdf_files[choice_num])
            except (ValueError, IndexError):
                print("Invalid selection!")
                exit(1)

    # Generate output filename
    output_html = Path(pdf_file).stem + ".html"

    print(f"\nInput: {pdf_file}")
    print(f"Output: {output_html}")

    print("\nChoose conversion method:")
    print("1. pdfplumber (simple text extraction)")
    print("2. PyMuPDF (better formatting)")
    print("3. pdfminer (detailed control)")
    print("4. pdf2htmlEX (pixel-perfect, requires external tool)")

    choice = input("Enter choice (1-4): ")

    try:
        if choice == "1":
            pdf_to_html_pdfplumber(pdf_file, output_html)
        elif choice == "2":
            pdf_to_html_pymupdf(pdf_file, output_html)
        elif choice == "3":
            pdf_to_html_pdfminer(pdf_file, output_html)
        elif choice == "4":
            pdf_to_html_pdf2htmlex(pdf_file, "output")
        else:
            print("Invalid choice!")
    except Exception as e:
        print(f"Error during conversion: {e}")

    # Batch conversion example
    # batch_convert_pdfs("input_pdfs", "output_html", "pymupdf")