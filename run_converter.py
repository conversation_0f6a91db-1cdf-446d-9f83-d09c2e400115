#!/usr/bin/env python3
"""
Universal File Converter Launcher
Checks dependencies and launches the GUI application
"""

import sys
import subprocess
import importlib.util
from pathlib import Path

def check_dependency(package_name, install_name=None):
    """Check if a package is installed"""
    if install_name is None:
        install_name = package_name
        
    spec = importlib.util.find_spec(package_name)
    return spec is not None, install_name

def install_package(package_name):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("🔄 Universal File Converter")
    print("=" * 50)
    
    # Check required dependencies
    dependencies = [
        ("markdown", "markdown"),
        ("pdfplumber", "pdfplumber"),
        ("fitz", "PyMuPDF"),
        ("pdfminer", "pdfminer.six")
    ]
    
    missing_deps = []
    
    print("Checking dependencies...")
    for package, install_name in dependencies:
        is_installed, _ = check_dependency(package, install_name)
        status = "✅" if is_installed else "❌"
        print(f"  {status} {install_name}")
        
        if not is_installed:
            missing_deps.append(install_name)
    
    # Install missing dependencies
    if missing_deps:
        print(f"\n⚠️  Missing dependencies: {', '.join(missing_deps)}")
        
        install_choice = input("\nWould you like to install missing dependencies? (y/n): ").lower().strip()
        
        if install_choice in ['y', 'yes']:
            print("\nInstalling dependencies...")
            for dep in missing_deps:
                print(f"Installing {dep}...")
                if install_package(dep):
                    print(f"✅ {dep} installed successfully")
                else:
                    print(f"❌ Failed to install {dep}")
                    print(f"   Please install manually: pip install {dep}")
        else:
            print("\n⚠️  Some features may not work without all dependencies.")
    
    # Check if GUI file exists
    gui_file = Path(__file__).parent / "unified_converter_gui.py"
    if not gui_file.exists():
        print(f"\n❌ GUI file not found: {gui_file}")
        print("Please ensure unified_converter_gui.py is in the same directory.")
        return
    
    # Check if CSS themes exist
    themes_dir = Path(__file__).parent / "css_themes"
    if not themes_dir.exists():
        print(f"\n⚠️  CSS themes directory not found: {themes_dir}")
        print("Some theme features may not work.")
    
    print("\n🚀 Launching Universal File Converter...")
    print("=" * 50)
    
    try:
        # Import and run the GUI
        sys.path.insert(0, str(Path(__file__).parent))
        from unified_converter_gui import main as run_gui
        run_gui()
        
    except ImportError as e:
        print(f"❌ Error importing GUI: {e}")
        print("Please check that all files are in the correct location.")
    except Exception as e:
        print(f"❌ Error running application: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
