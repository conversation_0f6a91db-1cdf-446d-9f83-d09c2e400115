/* Presentation Slides Theme - Full-screen slide deck */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap');

:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --background-color: #ffffff;
  --surface-color: #f8fafc;
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-light: #718096;
  --border-color: #e2e8f0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  margin: 0;
  padding: 0;
  background: var(--background-color);
  color: var(--text-primary);
  overflow-x: hidden;
  line-height: 1.6;
}

.presentation-container {
  width: 100vw;
  min-height: 100vh;
  scroll-snap-type: y mandatory;
  overflow-y: auto;
}

/* Slide Structure */
.slide {
  width: 100vw;
  min-height: 100vh;
  padding: 4rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  scroll-snap-align: start;
  position: relative;
  background: var(--background-color);
}

.slide-content {
  max-width: 1000px;
  width: 100%;
  z-index: 2;
}

/* Slide Types */
.slide-title {
  background: var(--gradient-primary);
  color: white;
  position: relative;
  overflow: hidden;
}

.slide-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  z-index: 1;
}

.slide-section {
  background: var(--surface-color);
  border-top: 4px solid var(--primary-color);
}

.slide-content-split {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  text-align: left;
}

.slide-image {
  background: var(--gradient-accent);
  color: white;
}

/* Typography */
h1 {
  font-size: 4rem;
  font-weight: 800;
  margin: 0 0 2rem 0;
  line-height: 1.1;
}

.slide-title h1 {
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

h2 {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
  color: var(--primary-color);
  line-height: 1.2;
}

h3 {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  color: var(--secondary-color);
}

h4 {
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
}

p {
  font-size: 1.25rem;
  margin: 1rem 0;
  color: var(--text-primary);
}

.slide-title p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.5rem;
}

.subtitle {
  font-size: 1.5rem;
  color: var(--text-secondary);
  font-weight: 300;
  margin-bottom: 3rem;
}

.slide-title .subtitle {
  color: rgba(255, 255, 255, 0.8);
}

/* Lists */
ul, ol {
  text-align: left;
  font-size: 1.25rem;
  margin: 2rem 0;
  max-width: 800px;
}

li {
  margin: 1rem 0;
  padding-left: 0.5rem;
}

ul li::marker {
  color: var(--primary-color);
}

/* Big Numbers/Stats */
.big-number {
  font-size: 6rem;
  font-weight: 800;
  color: var(--primary-color);
  line-height: 1;
  margin: 1rem 0;
}

.big-number-label {
  font-size: 1.5rem;
  color: var(--text-secondary);
  font-weight: 300;
}

/* Code */
code {
  font-family: 'JetBrains Mono', monospace;
  background: var(--surface-color);
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  font-size: 1rem;
  color: var(--primary-color);
  border: 1px solid var(--border-color);
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  overflow-x: auto;
  margin: 2rem 0;
  text-align: left;
  max-width: 100%;
}

pre code {
  background: none;
  border: none;
  padding: 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

/* Blockquotes */
blockquote {
  font-size: 2rem;
  font-style: italic;
  color: var(--primary-color);
  border: none;
  margin: 2rem 0;
  position: relative;
  font-weight: 300;
}

blockquote::before {
  content: '"';
  font-size: 8rem;
  color: var(--accent-color);
  position: absolute;
  top: -3rem;
  left: -2rem;
  opacity: 0.3;
  font-family: serif;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 10px 30px var(--shadow-color);
  margin: 2rem 0;
}

.image-full {
  width: 100%;
  max-height: 70vh;
  object-fit: cover;
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: var(--background-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px var(--shadow-color);
  font-size: 1.1rem;
}

th, td {
  padding: 1.5rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background: var(--gradient-primary);
  color: white;
  font-weight: 600;
}

tr:hover {
  background: var(--surface-color);
}

/* Call-to-Action */
.cta {
  background: var(--gradient-primary);
  color: white;
  padding: 3rem;
  border-radius: 20px;
  margin: 3rem 0;
  box-shadow: 0 10px 30px var(--shadow-color);
}

.cta h3 {
  color: white;
  margin-top: 0;
}

.cta-button {
  display: inline-block;
  background: white;
  color: var(--primary-color);
  padding: 1rem 3rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.25rem;
  margin-top: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Navigation */
.slide-nav {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
  display: flex;
  gap: 1rem;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--text-light);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot.active {
  background: var(--primary-color);
  transform: scale(1.3);
}

/* Slide Counter */
.slide-counter {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  color: var(--text-secondary);
  backdrop-filter: blur(10px);
}

/* Progress Bar */
.progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  height: 4px;
  background: var(--gradient-primary);
  z-index: 1000;
  transition: width 0.3s ease;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-content > * {
  animation: slideInUp 0.6s ease-out;
}

/* Two-column layout */
.two-column {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
  text-align: left;
}

/* Three-column layout */
.three-column {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 2rem;
  text-align: center;
}

/* Feature boxes */
.feature-box {
  background: var(--background-color);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px var(--shadow-color);
  border-top: 4px solid var(--primary-color);
}

.feature-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .slide {
    padding: 2rem 1rem;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  .slide-content-split,
  .two-column,
  .three-column {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .big-number {
    font-size: 4rem;
  }
  
  blockquote {
    font-size: 1.5rem;
  }
  
  p, li {
    font-size: 1.1rem;
  }
}

/* Print Styles */
@media print {
  .slide {
    page-break-after: always;
    min-height: auto;
    padding: 2rem;
  }
  
  .slide-nav,
  .slide-counter,
  .progress-bar {
    display: none;
  }
  
  .slide-title {
    background: none !important;
    color: var(--text-primary) !important;
  }
  
  .slide-title h1,
  .slide-title p,
  .slide-title .subtitle {
    color: var(--text-primary) !important;
  }
}
