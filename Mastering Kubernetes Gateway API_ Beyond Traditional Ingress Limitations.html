
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>PDF Content</title>
        <style>
            body { margin: 20px; font-family: Arial, sans-serif; }
            .page { margin-bottom: 40px; }
            .text-block { margin-bottom: 10px; }
        </style>
    </head>
    <body>
    <div class="page"><h3>Page 1</h3><div class="text-block"><span style="font-size: 18.0px; font-weight: bold;">Mastering Kubernetes Gateway API: Beyond Traditional</span><br><span style="font-size: 18.0px; font-weight: bold;">Ingress Limitations</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-style: italic;">A practical guide to implementing advanced traffic routing with Emissary Ingress on Azure Kubernetes</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-style: italic;">Service</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">Why I Moved Beyond Traditional Ingress</span><br></div><div class="text-block"><span style="font-size: 12.0px;">After years of working with Kubernetes Ingress, I found myself constantly hitting walls when trying</span><br></div><div class="text-block"><span style="font-size: 12.0px;">to implement modern application requirements:</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Header-based routing</span><span style="font-size: 12.0px;"> for A/B testing? </span><span style="font-size: 12.0px; font-style: italic;">Not possible with standard Ingress.</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Query parameter routing</span><span style="font-size: 12.0px;"> for API versioning? </span><span style="font-size: 12.0px; font-style: italic;">Nope.</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Standardized traffic splitting</span><span style="font-size: 12.0px;"> for canary deployments? </span><span style="font-size: 12.0px; font-style: italic;">Vendor-specific annotations only.</span><br></div><div class="text-block"><span style="font-size: 12.0px;">That's when I discovered </span><span style="font-size: 12.0px; font-weight: bold;">Kubernetes Gateway API</span><span style="font-size: 12.0px;"> - the next generation of ingress that makes</span><br></div><div class="text-block"><span style="font-size: 12.0px;">these "impossible" scenarios not just possible, but elegant.</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">The Gateway API Advantage</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Traditional Ingress was designed for simple HTTP routing. Gateway API was built for the</span><br></div><div class="text-block"><span style="font-size: 12.0px;">complexities of modern microservices:</span><br></div></div><div class="page"><h3>Page 2</h3><div class="text-block"><span style="font-size: 10.5px; font-weight: bold;">Feature</span><br><span style="font-size: 10.5px; font-weight: bold;">Traditional Ingress</span><br><span style="font-size: 10.5px; font-weight: bold;">Gateway API</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-weight: bold;">Header-based routing</span><br><span style="font-size: 10.5px;">❌</span><br><span style="font-size: 10.5px;">✅</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px; font-weight: bold;">Native support</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-weight: bold;">Query parameter routing</span><br><span style="font-size: 10.5px;">❌</span><br><span style="font-size: 10.5px;">✅</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px; font-weight: bold;">Built-in</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-weight: bold;">Traffic splitting</span><br><span style="font-size: 10.5px;">⚠️</span><span style="font-size: 10.5px;"> Annotations</span><br><span style="font-size: 10.5px;">✅</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px; font-weight: bold;">Standardized</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-weight: bold;">Cross-namespace routing</span><br><span style="font-size: 10.5px;">❌</span><br><span style="font-size: 10.5px;">✅</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px; font-weight: bold;">Designed for it</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-weight: bold;">Request transformation</span><br><span style="font-size: 10.5px;">⚠️</span><span style="font-size: 10.5px;"> Vendor-specific</span><br><span style="font-size: 10.5px;">✅</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px; font-weight: bold;">Portable</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">My Implementation Journey</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">Environment Setup</span><br></div><div class="text-block"><span style="font-size: 12.0px;">I chose </span><span style="font-size: 12.0px; font-weight: bold;">Azure Kubernetes Service (AKS)</span><span style="font-size: 12.0px;"> with </span><span style="font-size: 12.0px; font-weight: bold;">Emissary Ingress</span><span style="font-size: 12.0px;"> as my Gateway API controller.</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Here's my complete setup:</span><br></div></div><div class="page"><h3>Page 3</h3><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">Gateway API Architecture</span><br></div><div class="text-block"><span style="font-size: 12.0px;">The beauty of Gateway API lies in its clear separation of concerns:</span><br></div><div class="text-block"><span style="font-size: 9.0px;">powershell</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Create AKS cluster</span><br></div><div class="text-block"><span style="font-size: 10.5px;">az aks create \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--</span><span style="font-size: 10.5px;">resource-</span><span style="font-size: 10.5px;">group</span><span style="font-size: 10.5px;"> gateway-api-rg \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--</span><span style="font-size: 10.5px;">name gatewayakscluster \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--</span><span style="font-size: 10.5px;">node-count 2 \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--</span><span style="font-size: 10.5px;">node-vm-size Standard_D2s_v3 \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--enable-addons</span><span style="font-size: 10.5px;"> monitoring \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--</span><span style="font-size: 10.5px;">generate-ssh-keys</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Install Gateway API CRDs</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kubectl apply </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">f https:</span><span style="font-size: 10.5px;">//</span><span style="font-size: 10.5px;">github.com/kubernetes-sigs/gateway-api/releases/download/v1.0.0/s</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Install Emissary Ingress with Azure integration</span><br></div><div class="text-block"><span style="font-size: 10.5px;">helm install emissary-ingress datawire/emissary-ingress \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--</span><span style="font-size: 10.5px;">namespace ambassador \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--</span><span style="font-size: 10.5px;">create-namespace \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--set</span><span style="font-size: 10.5px;"> service.</span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">=LoadBalancer \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">--set</span><span style="font-size: 10.5px;"> service.annotations.</span><span style="font-size: 10.5px;">"service\.beta\.kubernetes\.io/azure-dns-label-name"</span><span style="font-size: 10.5px;">=</span><span style="font-size: 10.5px;">"ambassad</span><br></div></div><div class="page"><h3>Page 4</h3><div class="text-block"><span style="font-size: 9.0px;">yaml</span><br></div></div><div class="page"><h3>Page 5</h3><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># 1. GatewayClass - Defines the controller</span><br></div><div class="text-block"><span style="font-size: 10.5px;">apiVersion</span><span style="font-size: 10.5px;">: gateway.networking.k8s.io/v1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kind</span><span style="font-size: 10.5px;">: GatewayClass</span><br></div><div class="text-block"><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: ambassador</span><br></div><div class="text-block"><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">controllerName</span><span style="font-size: 10.5px;">: getambassador.io/gateway-controller</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">description</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">"Ambassador Edge Stack Gateway Class"</span><br></div><div class="text-block"><span style="font-size: 10.5px;">---</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># 2. Gateway - Defines listeners and TLS</span><br></div><div class="text-block"><span style="font-size: 10.5px;">apiVersion</span><span style="font-size: 10.5px;">: gateway.networking.k8s.io/v1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kind</span><span style="font-size: 10.5px;">: Gateway</span><br></div><div class="text-block"><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: ambassador-gateway</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: ambassador</span><br></div><div class="text-block"><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">gatewayClassName</span><span style="font-size: 10.5px;">: ambassador</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">listeners</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: http</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">protocol</span><span style="font-size: 10.5px;">: HTTP</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">allowedRoutes</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">namespaces</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">from</span><span style="font-size: 10.5px;">: All</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: https</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">443</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">protocol</span><span style="font-size: 10.5px;">: HTTPS</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">allowedRoutes</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">namespaces</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">from</span><span style="font-size: 10.5px;">: All</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">tls</span><span style="font-size: 10.5px;">:</span><br></div></div><div class="page"><h3>Page 6</h3><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">Real-World Application Setup</span><br></div><div class="text-block"><span style="font-size: 12.0px;">I created two microservices to demonstrate Gateway API capabilities:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">mode</span><span style="font-size: 10.5px;">: Terminate</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">certificateRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: gateway-tls-cert</span><br></div></div><div class="page"><h3>Page 7</h3><div class="text-block"><span style="font-size: 9.0px;">yaml</span><br></div></div><div class="page"><h3>Page 8</h3><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Web Application</span><br></div><div class="text-block"><span style="font-size: 10.5px;">apiVersion</span><span style="font-size: 10.5px;">: apps/v1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kind</span><span style="font-size: 10.5px;">: Deployment</span><br></div><div class="text-block"><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: web-app</span><br></div><div class="text-block"><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">replicas</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">2</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">selector</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">matchLabels</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">app</span><span style="font-size: 10.5px;">: web-app</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">template</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">labels</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">app</span><span style="font-size: 10.5px;">: web-app</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">containers</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: web-app</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">image</span><span style="font-size: 10.5px;">: nginx:latest</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">ports</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        - </span><span style="font-size: 10.5px;">containerPort</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">volumeMounts</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: html</span><br></div><div class="text-block"><span style="font-size: 10.5px;">          </span><span style="font-size: 10.5px;">mountPath</span><span style="font-size: 10.5px;">: /usr/share/nginx/html</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">volumes</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: html</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">configMap</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">          </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: web-html</span><br></div><div class="text-block"><span style="font-size: 10.5px;">---</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># API Service (returns JSON)</span><br></div><div class="text-block"><span style="font-size: 10.5px;">apiVersion</span><span style="font-size: 10.5px;">: apps/v1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kind</span><span style="font-size: 10.5px;">: Deployment</span><br></div><div class="text-block"><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div></div><div class="page"><h3>Page 9</h3><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">Gateway API Features That Transformed My Architecture</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">1. Basic Host-Based Routing</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Starting simple - route different hostnames to different services:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: api-app</span><br></div><div class="text-block"><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">replicas</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">2</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">selector</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">matchLabels</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">app</span><span style="font-size: 10.5px;">: api-app</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">template</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">labels</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">app</span><span style="font-size: 10.5px;">: api-app</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">containers</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: api-app</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">image</span><span style="font-size: 10.5px;">: nginx:latest</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px; font-style: italic;"># ... configured to return JSON responses</span><br></div></div><div class="page"><h3>Page 10</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Testing:</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">2. Advanced Path-Based Routing with URL Rewriting</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Here's where Gateway API starts to shine - sophisticated path handling:</span><br></div><div class="text-block"><span style="font-size: 9.0px;">yaml</span><br></div><div class="text-block"><span style="font-size: 10.5px;">apiVersion</span><span style="font-size: 10.5px;">: gateway.networking.k8s.io/v1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kind</span><span style="font-size: 10.5px;">: HTTPRoute</span><br></div><div class="text-block"><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: web-route</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: default</span><br></div><div class="text-block"><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">parentRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: ambassador-gateway</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: ambassador</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">hostnames</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - web.example.com</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">rules</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">matches</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">path</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: PathPrefix</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: /</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">backendRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: web-service</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div><div class="text-block"><span style="font-size: 9.0px;">bash</span><br></div><div class="text-block"><span style="font-size: 10.5px;">curl</span><span style="font-size: 10.5px;"> -H </span><span style="font-size: 10.5px;">"Host: web.example.com"</span><span style="font-size: 10.5px;"> http://your-gateway-ip/</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Returns: Web Application HTML</span><br></div></div><div class="page"><h3>Page 11</h3><div class="text-block"><span style="font-size: 9.0px;">yaml</span><br></div></div><div class="page"><h3>Page 12</h3><div class="text-block"><span style="font-size: 10.5px;">apiVersion</span><span style="font-size: 10.5px;">: gateway.networking.k8s.io/v1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kind</span><span style="font-size: 10.5px;">: HTTPRoute</span><br></div><div class="text-block"><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: app-route</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: default</span><br></div><div class="text-block"><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">parentRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: ambassador-gateway</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: ambassador</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">hostnames</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - app.example.com</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">rules</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px; font-style: italic;"># API routes with path rewriting</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">matches</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">path</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: PathPrefix</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: /api</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">filters</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: URLRewrite</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">urlRewrite</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">path</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">          </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: ReplacePrefixMatch</span><br></div><div class="text-block"><span style="font-size: 10.5px;">          </span><span style="font-size: 10.5px;">replacePrefixMatch</span><span style="font-size: 10.5px;">: /</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: RequestHeaderModifier</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">requestHeaderModifier</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">add</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: X-Service-Type</span><br></div><div class="text-block"><span style="font-size: 10.5px;">          </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: API</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">backendRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: api-service</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><br></div></div><div class="page"><h3>Page 13</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">What this achieves:</span><br></div><div class="text-block"><span style="font-size: 10.799999237060547px;">app.example.com/api/users</span><span style="font-size: 12.0px;">  → </span><span style="font-size: 10.799999237060547px;">api-service/users</span><span style="font-size: 12.0px;">  (removes </span><span style="font-size: 10.799999237060547px;">/api</span><span style="font-size: 12.0px;">  prefix)</span><br></div><div class="text-block"><span style="font-size: 10.799999237060547px;">app.example.com/web/dashboard</span><span style="font-size: 12.0px;">  → </span><span style="font-size: 10.799999237060547px;">web-service/dashboard</span><span style="font-size: 12.0px;">  (removes </span><span style="font-size: 10.799999237060547px;">/web</span><span style="font-size: 12.0px;">  prefix)</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Adds service-type headers for observability</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">3. Traffic Splitting for Canary Deployments</span><br></div><div class="text-block"><span style="font-size: 12.0px;">This is where Gateway API truly surpasses traditional Ingress:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px; font-style: italic;"># Web routes</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">matches</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">path</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: PathPrefix</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: /web</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">filters</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: URLRewrite</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">urlRewrite</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">path</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">          </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: ReplacePrefixMatch</span><br></div><div class="text-block"><span style="font-size: 10.5px;">          </span><span style="font-size: 10.5px;">replacePrefixMatch</span><span style="font-size: 10.5px;">: /</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: RequestHeaderModifier</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">requestHeaderModifier</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">add</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: X-Service-Type</span><br></div><div class="text-block"><span style="font-size: 10.5px;">          </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: WEB</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">backendRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: web-service</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div></div><div class="page"><h3>Page 14</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Result:</span><span style="font-size: 12.0px;"> 90% of traffic goes to stable service, 10% to canary - </span><span style="font-size: 12.0px; font-weight: bold;">no vendor-specific annotations</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">required!</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">4. Header-Based Routing (Impossible with Traditional Ingress)</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Here's a game-changer for user segmentation:</span><br></div><div class="text-block"><span style="font-size: 9.0px;">yaml</span><br></div><div class="text-block"><span style="font-size: 10.5px;">apiVersion</span><span style="font-size: 10.5px;">: gateway.networking.k8s.io/v1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kind</span><span style="font-size: 10.5px;">: HTTPRoute</span><br></div><div class="text-block"><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: canary-route</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: default</span><br></div><div class="text-block"><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">parentRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: ambassador-gateway</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: ambassador</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">hostnames</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - canary.example.com</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">rules</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">matches</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">path</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: PathPrefix</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: /</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">backendRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: web-service      </span><span style="font-size: 10.5px; font-style: italic;"># Stable version</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">weight</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">90</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: api-service      </span><span style="font-size: 10.5px; font-style: italic;"># Canary version  </span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">weight</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">10</span><br></div></div><div class="page"><h3>Page 15</h3><div class="text-block"><span style="font-size: 9.0px;">yaml</span><br></div><div class="text-block"><span style="font-size: 10.5px;">apiVersion</span><span style="font-size: 10.5px;">: gateway.networking.k8s.io/v1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kind</span><span style="font-size: 10.5px;">: HTTPRoute</span><br></div><div class="text-block"><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: header-route</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: default</span><br></div><div class="text-block"><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">parentRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: ambassador-gateway</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: ambassador</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">hostnames</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - header.example.com</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">rules</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px; font-style: italic;"># Premium users get enhanced API</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">matches</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">headers</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: x-user-type</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: premium</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">backendRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: api-service</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px; font-style: italic;"># Regular users get standard service</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">matches</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">path</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: PathPrefix</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: /</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">backendRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: web-service</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div></div><div class="page"><h3>Page 16</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Testing:</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">5. Query Parameter Routing (Also Impossible with Ingress)</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Perfect for API versioning:</span><br></div><div class="text-block"><span style="font-size: 9.0px;">bash</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Premium user experience</span><br></div><div class="text-block"><span style="font-size: 10.5px;">curl</span><span style="font-size: 10.5px;"> -H </span><span style="font-size: 10.5px;">"Host: header.example.com"</span><span style="font-size: 10.5px;"> -H </span><span style="font-size: 10.5px;">"x-user-type: premium"</span><span style="font-size: 10.5px;"> http://your-gateway-ip/</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Returns: Enhanced API response</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Regular user experience  </span><br></div><div class="text-block"><span style="font-size: 10.5px;">curl</span><span style="font-size: 10.5px;"> -H </span><span style="font-size: 10.5px;">"Host: header.example.com"</span><span style="font-size: 10.5px;"> http://your-gateway-ip/</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Returns: Standard web response</span><br></div></div><div class="page"><h3>Page 17</h3><div class="text-block"><span style="font-size: 9.0px;">yaml</span><br></div><div class="text-block"><span style="font-size: 10.5px;">apiVersion</span><span style="font-size: 10.5px;">: gateway.networking.k8s.io/v1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kind</span><span style="font-size: 10.5px;">: HTTPRoute</span><br></div><div class="text-block"><span style="font-size: 10.5px;">metadata</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: query-route</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: default</span><br></div><div class="text-block"><span style="font-size: 10.5px;">spec</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">parentRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: ambassador-gateway</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: ambassador</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">hostnames</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - query.example.com</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">rules</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px; font-style: italic;"># Version 2 API</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">matches</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">queryParams</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: version</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: v2</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">backendRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: api-service</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px; font-style: italic;"># Default to version 1</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">matches</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">path</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: PathPrefix</span><br></div><div class="text-block"><span style="font-size: 10.5px;">        </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: /</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">backendRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: web-service</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">port</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">80</span><br></div></div><div class="page"><h3>Page 18</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Usage:</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">Testing the Complete Implementation</span><br></div><div class="text-block"><span style="font-size: 12.0px;">With port-forwarding for local testing:</span><br></div><div class="text-block"><span style="font-size: 9.0px;">bash</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Access v2 API</span><br></div><div class="text-block"><span style="font-size: 10.5px;">curl</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">"http://your-gateway-ip/?version=v2"</span><span style="font-size: 10.5px;"> -H </span><span style="font-size: 10.5px;">"Host: query.example.com"</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Default API</span><br></div><div class="text-block"><span style="font-size: 10.5px;">curl</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">"http://your-gateway-ip/"</span><span style="font-size: 10.5px;"> -H </span><span style="font-size: 10.5px;">"Host: query.example.com"</span><br></div></div><div class="page"><h3>Page 19</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">All tests returned HTTP 200 with appropriate responses</span><span style="font-size: 12.0px;"> - proving the Gateway API</span><br></div><div class="text-block"><span style="font-size: 12.0px;">implementation works perfectly!</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">Key Learnings and Best Practices</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">1. Resource Organization</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">GatewayClass</span><span style="font-size: 12.0px;">: One per controller type (Emissary, Istio, etc.)</span><br></div><div class="text-block"><span style="font-size: 9.0px;">powershell</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Port forward to test Gateway API locally</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kubectl port-forward </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">n ambassador svc/emissary-ingress 8080:80</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Test all implemented features</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Basic routing</span><br></div><div class="text-block"><span style="font-size: 10.5px;">Invoke-WebRequest</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Uri </span><span style="font-size: 10.5px;">"http://localhost:8080/"</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Headers @{</span><span style="font-size: 10.5px;">"Host"</span><span style="font-size: 10.5px;">=</span><span style="font-size: 10.5px;">"web.example.com"</span><span style="font-size: 10.5px;">}</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Advanced path routing</span><br></div><div class="text-block"><span style="font-size: 10.5px;">Invoke-WebRequest</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Uri </span><span style="font-size: 10.5px;">"http://localhost:8080/api"</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Headers @{</span><span style="font-size: 10.5px;">"Host"</span><span style="font-size: 10.5px;">=</span><span style="font-size: 10.5px;">"app.example.com"</span><span style="font-size: 10.5px;">}</span><br></div><div class="text-block"><span style="font-size: 10.5px;">Invoke-WebRequest</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Uri </span><span style="font-size: 10.5px;">"http://localhost:8080/web"</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Headers @{</span><span style="font-size: 10.5px;">"Host"</span><span style="font-size: 10.5px;">=</span><span style="font-size: 10.5px;">"app.example.com"</span><span style="font-size: 10.5px;">}</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Canary deployment (run multiple times to see 90/10 split)</span><br></div><div class="text-block"><span style="font-size: 10.5px;">1..10 | </span><span style="font-size: 10.5px;">ForEach-Object</span><span style="font-size: 10.5px;"> {</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">Invoke-WebRequest</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Uri </span><span style="font-size: 10.5px;">"http://localhost:8080/"</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Headers @{</span><span style="font-size: 10.5px;">"Host"</span><span style="font-size: 10.5px;">=</span><span style="font-size: 10.5px;">"canary.example.com"</span><br></div><div class="text-block"><span style="font-size: 10.5px;">}</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Header-based routing</span><br></div><div class="text-block"><span style="font-size: 10.5px;">Invoke-WebRequest</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Uri </span><span style="font-size: 10.5px;">"http://localhost:8080/"</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Headers @{</span><span style="font-size: 10.5px;">"Host"</span><span style="font-size: 10.5px;">=</span><span style="font-size: 10.5px;">"header.example.com"</span><span style="font-size: 10.5px;">; </span><span style="font-size: 10.5px;">"x</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Query parameter routing</span><br></div><div class="text-block"><span style="font-size: 10.5px;">Invoke-WebRequest</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Uri </span><span style="font-size: 10.5px;">"http://localhost:8080/?version=v2"</span><span style="font-size: 10.5px;"> </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">Headers @{</span><span style="font-size: 10.5px;">"Host"</span><span style="font-size: 10.5px;">=</span><span style="font-size: 10.5px;">"query.exampl</span><br></div></div><div class="page"><h3>Page 20</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Gateway</span><span style="font-size: 12.0px;">: One per environment/domain (dev, staging, prod)</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">HTTPRoutes</span><span style="font-size: 12.0px;">: One per logical application/service grouping</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">2. Cross-Namespace Capabilities</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Gateway API's design shines with its cross-namespace support:</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Gateway in </span><span style="font-size: 10.799999237060547px;">ambassador</span><span style="font-size: 12.0px;">  namespace</span><br></div><div class="text-block"><span style="font-size: 12.0px;">HTTPRoutes in </span><span style="font-size: 10.799999237060547px;">default</span><span style="font-size: 12.0px;">  namespace</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Perfect for platform team (manages gateways) + dev teams (manage routes)</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">3. Future-Proof Architecture</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Gateway API is designed for extensibility:</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Policy attachment points</span><span style="font-size: 12.0px;"> for security policies</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Filter extension points</span><span style="font-size: 12.0px;"> for custom transformations</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Service mesh integration</span><span style="font-size: 12.0px;"> ready</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">4. Production Considerations</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">TLS Management:</span><br></div><div class="text-block"><span style="font-size: 9.0px;">yaml</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Automated certificate management with cert-manager</span><br></div><div class="text-block"><span style="font-size: 10.5px;">tls</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">mode</span><span style="font-size: 10.5px;">: Terminate</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">certificateRefs</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: app-tls-cert</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">namespace</span><span style="font-size: 10.5px;">: ambassador</span><br></div></div><div class="page"><h3>Page 21</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Observability Integration:</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">When to Choose Gateway API Over Ingress</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Choose Gateway API when you need:</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> </span><span style="font-size: 12.0px; font-weight: bold;">Header or query parameter routing</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> </span><span style="font-size: 12.0px; font-weight: bold;">Standardized traffic splitting</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> </span><span style="font-size: 12.0px; font-weight: bold;">Cross-namespace route management</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> </span><span style="font-size: 12.0px; font-weight: bold;">Future-proof API design</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> </span><span style="font-size: 12.0px; font-weight: bold;">Service mesh integration</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Stick with Ingress when:</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> </span><span style="font-size: 12.0px; font-weight: bold;">Simple host/path routing only</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> </span><span style="font-size: 12.0px; font-weight: bold;">Team already expert in Ingress</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> </span><span style="font-size: 12.0px; font-weight: bold;">Legacy application constraints</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">Infrastructure Challenges and Solutions</span><br></div><div class="text-block"><span style="font-size: 9.0px;">yaml</span><br></div><div class="text-block"><span style="font-size: 10.5px; font-style: italic;"># Request header modification for tracing</span><br></div><div class="text-block"><span style="font-size: 10.5px;">filters</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">- </span><span style="font-size: 10.5px;">type</span><span style="font-size: 10.5px;">: RequestHeaderModifier</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">requestHeaderModifier</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    </span><span style="font-size: 10.5px;">add</span><span style="font-size: 10.5px;">:</span><br></div><div class="text-block"><span style="font-size: 10.5px;">    - </span><span style="font-size: 10.5px;">name</span><span style="font-size: 10.5px;">: X-Trace-ID</span><br></div><div class="text-block"><span style="font-size: 10.5px;">      </span><span style="font-size: 10.5px;">value</span><span style="font-size: 10.5px;">: </span><span style="font-size: 10.5px;">"generated-trace-id"</span><br></div></div><div class="page"><h3>Page 22</h3><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">The NodePort Learning Experience</span><br></div><div class="text-block"><span style="font-size: 12.0px;">During implementation, I encountered AKS NodePort connectivity issues that taught valuable</span><br></div><div class="text-block"><span style="font-size: 12.0px;">lessons:</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Problem:</span><span style="font-size: 12.0px;"> External LoadBalancer health probes failing </span><span style="font-size: 12.0px; font-weight: bold;">Root Cause:</span><span style="font-size: 12.0px;"> Missing health endpoint</span><br></div><div class="text-block"><span style="font-size: 12.0px;">configuration </span><span style="font-size: 12.0px; font-weight: bold;">Solution:</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Key Insight:</span><span style="font-size: 12.0px;"> Gateway API configuration was perfect; infrastructure integration required additional</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Azure-specific annotations.</span><br></div><div class="text-block"><span style="font-size: 13.5px; font-weight: bold;">Production Deployment Strategies</span><br></div><div class="text-block"><span style="font-size: 12.0px;">For production Gateway API deployments:</span><br></div><div class="text-block"><span style="font-size: 12.0px;">1. </span><span style="font-size: 12.0px; font-weight: bold;">Use managed ingress controllers</span><span style="font-size: 12.0px;"> (AGIC, Istio, etc.)</span><br></div><div class="text-block"><span style="font-size: 12.0px;">2. </span><span style="font-size: 12.0px; font-weight: bold;">Implement proper health checks</span><br></div><div class="text-block"><span style="font-size: 12.0px;">3. </span><span style="font-size: 12.0px; font-weight: bold;">Configure TLS with cert-manager</span><br></div><div class="text-block"><span style="font-size: 12.0px;">4. </span><span style="font-size: 12.0px; font-weight: bold;">Set up monitoring and alerting</span><br></div><div class="text-block"><span style="font-size: 12.0px;">5. </span><span style="font-size: 12.0px; font-weight: bold;">Plan for gradual migration</span><span style="font-size: 12.0px;"> from existing Ingress</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">The Future of Kubernetes Traffic Management</span><br></div><div class="text-block"><span style="font-size: 12.0px;">Gateway API represents a fundamental shift in how we think about traffic routing:</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Vendor neutrality</span><span style="font-size: 12.0px;">: Same config works across Istio, Envoy, NGINX, etc.</span><br></div><div class="text-block"><span style="font-size: 9.0px;">powershell</span><br></div><div class="text-block"><span style="font-size: 10.5px;">kubectl annotate service emissary-ingress </span><span style="font-size: 10.5px;">-</span><span style="font-size: 10.5px;">n ambassador \</span><br></div><div class="text-block"><span style="font-size: 10.5px;">  </span><span style="font-size: 10.5px;">"service.beta.kubernetes.io/azure-load-balancer-health-probe-request-path=/"</span><br></div></div><div class="page"><h3>Page 23</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Role-oriented design</span><span style="font-size: 12.0px;">: Platform teams manage infrastructure, dev teams manage routing</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Extensibility</span><span style="font-size: 12.0px;">: Ready for future protocols and features</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Service mesh ready</span><span style="font-size: 12.0px;">: Built with modern architecture in mind</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">Conclusion: Gateway API is Production Ready</span><br></div><div class="text-block"><span style="font-size: 12.0px;">After implementing complex routing scenarios that would have required vendor-specific hacks with</span><br></div><div class="text-block"><span style="font-size: 12.0px;">traditional Ingress, I'm convinced Gateway API is the future of Kubernetes traffic management.</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">What I achieved:</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> Header-based user segmentation</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> Query parameter API versioning</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> Standardized canary deployments</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> Path-based routing with URL rewriting</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> Cross-namespace resource management</span><br></div><div class="text-block"><span style="font-size: 12.0px;">✅</span><span style="font-size: 12.0px;"> TLS termination and security</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Your turn:</span><span style="font-size: 12.0px;"> Try implementing one of these scenarios with traditional Ingress, then with Gateway API.</span><br></div><div class="text-block"><span style="font-size: 12.0px;">The difference in complexity and maintainability is striking.</span><br></div><div class="text-block"><span style="font-size: 12.0px;">The days of wrestling with vendor-specific Ingress annotations are over. Gateway API provides the</span><br></div><div class="text-block"><span style="font-size: 12.0px;">elegant, standardized solution modern applications deserve.</span><br></div><div class="text-block"><span style="font-size: 15.0px; font-weight: bold;">Resources</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Gateway API Specification</span><span style="font-size: 12.0px;">: https://gateway-api.sigs.k8s.io/</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Emissary Ingress</span><span style="font-size: 12.0px;">: https://www.getambassador.io/docs/emissary/</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Azure Kubernetes Service</span><span style="font-size: 12.0px;">: https://docs.microsoft.com/en-us/azure/aks/</span><br></div></div><div class="page"><h3>Page 24</h3><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">Implementation Code</span><span style="font-size: 12.0px;">: Available on request</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-style: italic;">Ready to move beyond Ingress limitations? Gateway API is waiting for you.</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-weight: bold;">About the Author</span><span style="font-size: 12.0px;"> </span><span style="font-size: 12.0px; font-style: italic;">A cloud-native enthusiast exploring the cutting edge of Kubernetes networking</span><br></div><div class="text-block"><span style="font-size: 12.0px; font-style: italic;">and traffic management. Follow for more deep-dives into modern infrastructure patterns.</span><br></div></div></body></html>