<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG Evaluation Metrics Framework</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            max-width: 1400px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 2.5em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            text-align: center;
            color: #718096;
            margin-bottom: 40px;
            font-size: 1.1em;
        }
        
        .rag-flow {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin-bottom: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
        }
        
        .flow-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .flow-item:hover {
            transform: translateY(-5px);
        }
        
        .flow-arrow {
            font-size: 2em;
            color: #667eea;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .metric-category {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid;
        }
        
        .metric-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .category-answer {
            border-left-color: #48bb78;
        }
        
        .category-context {
            border-left-color: #4299e1;
        }
        
        .category-alignment {
            border-left-color: #ed8936;
        }
        
        .category-advanced {
            border-left-color: #9f7aea;
        }
        
        .category-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .category-icon {
            font-size: 1.5em;
        }
        
        .metric-item {
            margin: 10px 0;
            padding: 10px;
            background: #f7fafc;
            border-radius: 8px;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .metric-item:hover {
            background: #edf2f7;
            padding-left: 15px;
        }
        
        .metric-name {
            font-weight: 600;
            color: #2d3748;
            font-size: 1.05em;
        }
        
        .metric-desc {
            color: #718096;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .overlap-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #ffd700;
            color: #744210;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: bold;
        }
        
        .legend {
            margin-top: 40px;
            padding: 20px;
            background: #f7fafc;
            border-radius: 10px;
            text-align: center;
        }
        
        .legend-title {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .legend-items {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .rag-flow {
                flex-direction: column;
                gap: 20px;
            }
            
            .flow-arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 RAG Evaluation Metrics Framework</h1>
        <p class="subtitle">Comprehensive End-to-End Performance Assessment</p>
        
        <div class="rag-flow">
            <div class="flow-item">
                <div style="font-size: 2em;">📄</div>
                <div style="font-weight: bold; margin-top: 10px;">Query</div>
            </div>
            <div class="flow-arrow">→</div>
            <div class="flow-item">
                <div style="font-size: 2em;">🔍</div>
                <div style="font-weight: bold; margin-top: 10px;">Retrieval</div>
            </div>
            <div class="flow-arrow">→</div>
            <div class="flow-item">
                <div style="font-size: 2em;">🧠</div>
                <div style="font-weight: bold; margin-top: 10px;">Generation</div>
            </div>
            <div class="flow-arrow">→</div>
            <div class="flow-item">
                <div style="font-size: 2em;">✅</div>
                <div style="font-weight: bold; margin-top: 10px;">Answer</div>
            </div>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-category category-answer">
                <div class="category-title">
                    <span class="category-icon">✅</span>
                    Answer Quality Metrics
                </div>
                <div class="metric-item">
                    <div class="metric-name">Answer Correctness</div>
                    <div class="metric-desc">Overall accuracy and correctness of information</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Answer Completeness</div>
                    <div class="metric-desc">Fully addresses all aspects required by the question</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Answer Relevancy</div>
                    <div class="metric-desc">Pertinence to the specific question asked</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Answer Factual Correctness</div>
                    <div class="metric-desc">Verifiable factual claims against context</div>
                    <span class="overlap-badge">Overlaps</span>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Answer Trueness</div>
                    <div class="metric-desc">Factual accuracy, avoids false information</div>
                    <span class="overlap-badge">Overlaps</span>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Answer Similarity</div>
                    <div class="metric-desc">Semantic closeness to reference answer</div>
                </div>
            </div>
            
            <div class="metric-category category-context">
                <div class="category-title">
                    <span class="category-icon">🔍</span>
                    Context Metrics (RAGAS)
                </div>
                <div class="metric-item">
                    <div class="metric-name">Context Precision</div>
                    <div class="metric-desc">Proportion of retrieved chunks actually relevant</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Context Recall</div>
                    <div class="metric-desc">Proportion of all relevant info retrieved</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Context Relevancy</div>
                    <div class="metric-desc">Overall relevance (signal-to-noise ratio)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Contextual Precision</div>
                    <div class="metric-desc">Truly relevant retrieved chunks</div>
                    <span class="overlap-badge">Similar</span>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Contextual Recall</div>
                    <div class="metric-desc">All relevant info from source retrieved</div>
                    <span class="overlap-badge">Similar</span>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Contextual Relevancy</div>
                    <div class="metric-desc">Relevance evaluation of context</div>
                    <span class="overlap-badge">Similar</span>
                </div>
            </div>
            
            <div class="metric-category category-alignment">
                <div class="category-title">
                    <span class="category-icon">🔗</span>
                    Alignment Metrics
                </div>
                <div class="metric-item">
                    <div class="metric-name">Alignment Coverage</div>
                    <div class="metric-desc">Answer support and alignment with source</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Faithfulness</div>
                    <div class="metric-desc">Stays within retrieved context boundaries</div>
                    <span class="overlap-badge">Overlaps</span>
                </div>
            </div>
            
            <div class="metric-category category-advanced">
                <div class="category-title">
                    <span class="category-icon">🚀</span>
                    Advanced Metrics
                </div>
                <div class="metric-item">
                    <div class="metric-name">QAG Coverage</div>
                    <div class="metric-desc">Synthetic Q&A generation comprehensiveness</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">QAG Alignment</div>
                    <div class="metric-desc">Coherence of synthetic Q&A pairs</div>
                </div>
                <div class="metric-item">
                    <div class="metric-name">Reason Then Score</div>
                    <div class="metric-desc">Model provides reasoning before scoring</div>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-title">Metric Categories</div>
            <div class="legend-items">
                <div class="legend-item">
                    <div class="legend-color" style="background: #48bb78;"></div>
                    <span>Answer Quality</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4299e1;"></div>
                    <span>Context/Retrieval</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ed8936;"></div>
                    <span>Alignment</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #9f7aea;"></div>
                    <span>Advanced</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ffd700;"></div>
                    <span>Overlapping Metrics</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>