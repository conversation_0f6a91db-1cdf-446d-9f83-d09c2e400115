<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}}</title>
    <link rel="stylesheet" href="{{CSS_THEME}}">
    <style>
        /* Theme-specific wrapper classes */
        .modern-professional-wrapper { }
        .academic-paper-wrapper { }
        .blog-article-wrapper { }
        .github-readme-wrapper { }
        .presentation-slides-wrapper { }
    </style>
</head>
<body>
    <!-- Modern Professional Theme Structure -->
    <div class="container modern-professional-wrapper">
        {{CONTENT}}
    </div>

    <!-- Academic Paper Theme Structure -->
    <div class="container academic-paper-wrapper" style="display: none;">
        <div class="paper-header">
            <div class="paper-title">{{TITLE}}</div>
            <div class="paper-subtitle">{{SUBTITLE}}</div>
            <div class="paper-authors">{{AUTHORS}}</div>
            <div class="paper-affiliation">{{AFFILIATION}}</div>
            <div class="paper-date">{{DATE}}</div>
        </div>
        {{CONTENT}}
    </div>

    <!-- Blog Article Theme Structure -->
    <div class="container blog-article-wrapper" style="display: none;">
        <div class="article-header">
            <div class="article-meta">
                <div class="author-avatar">{{AUTHOR_INITIAL}}</div>
                <div class="article-info">
                    <div class="author-name">{{AUTHOR}}</div>
                    <div class="publish-date">{{DATE}}</div>
                </div>
                <div class="reading-time">{{READING_TIME}} min read</div>
            </div>
        </div>
        {{CONTENT}}
        
        <div class="social-share">
            <a href="#" class="share-button">Share on Twitter</a>
            <a href="#" class="share-button">Share on LinkedIn</a>
            <a href="#" class="share-button">Copy Link</a>
        </div>
    </div>

    <!-- GitHub README Theme Structure -->
    <div class="container github-readme-wrapper" style="display: none;">
        <div class="repo-header">
            <span class="repo-icon">📁</span>
            <a href="#" class="repo-name">{{REPO_NAME}}</a>
            <span class="repo-visibility">Public</span>
        </div>
        
        <div class="badges">
            <span class="badge badge-build">Build Passing</span>
            <span class="badge badge-version">v{{VERSION}}</span>
            <span class="badge badge-license">MIT License</span>
            <span class="badge badge-downloads">{{DOWNLOADS}} Downloads</span>
        </div>
        
        {{CONTENT}}
    </div>

    <!-- Presentation Slides Theme Structure -->
    <div class="presentation-container presentation-slides-wrapper" style="display: none;">
        <div class="progress-bar" style="width: 0%"></div>
        <div class="slide-counter">1 / {{TOTAL_SLIDES}}</div>
        
        <!-- Title Slide -->
        <div class="slide slide-title">
            <div class="slide-content">
                <h1>{{TITLE}}</h1>
                <p class="subtitle">{{SUBTITLE}}</p>
                <p>{{AUTHOR}} • {{DATE}}</p>
            </div>
        </div>
        
        <!-- Content slides will be generated from markdown -->
        {{SLIDES_CONTENT}}
        
        <div class="slide-nav">
            <div class="nav-dot active"></div>
            <div class="nav-dot"></div>
            <div class="nav-dot"></div>
        </div>
    </div>

    <script>
        // Theme switcher (for demo purposes)
        function switchTheme(themeName) {
            const wrappers = document.querySelectorAll('[class*="-wrapper"]');
            wrappers.forEach(wrapper => {
                wrapper.style.display = 'none';
            });
            
            const targetWrapper = document.querySelector(`.${themeName}-wrapper`);
            if (targetWrapper) {
                targetWrapper.style.display = 'block';
            }
        }

        // Presentation navigation
        if (document.querySelector('.presentation-slides-wrapper')) {
            let currentSlide = 0;
            const slides = document.querySelectorAll('.slide');
            const totalSlides = slides.length;
            
            function updateSlideCounter() {
                const counter = document.querySelector('.slide-counter');
                if (counter) {
                    counter.textContent = `${currentSlide + 1} / ${totalSlides}`;
                }
            }
            
            function updateProgressBar() {
                const progressBar = document.querySelector('.progress-bar');
                if (progressBar) {
                    const progress = ((currentSlide + 1) / totalSlides) * 100;
                    progressBar.style.width = `${progress}%`;
                }
            }
            
            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
                    if (currentSlide < totalSlides - 1) {
                        currentSlide++;
                        slides[currentSlide].scrollIntoView({ behavior: 'smooth' });
                        updateSlideCounter();
                        updateProgressBar();
                    }
                } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
                    if (currentSlide > 0) {
                        currentSlide--;
                        slides[currentSlide].scrollIntoView({ behavior: 'smooth' });
                        updateSlideCounter();
                        updateProgressBar();
                    }
                }
            });
        }
    </script>
</body>
</html>
