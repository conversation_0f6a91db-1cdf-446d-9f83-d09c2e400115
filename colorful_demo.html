<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>🌈 Super Colorful PDF to HTML Demo</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Super Colorful Enhanced CSS! */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #fad0c4 100%);
            min-height: 100vh;
            padding: 20px;
            animation: rainbowBackground 10s ease-in-out infinite;
        }
        
        @keyframes rainbowBackground {
            0%, 100% { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #fad0c4 100%); }
            20% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            40% { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
            60% { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
            80% { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #45b7d1 100%);
            color: white;
            padding: 30px;
            text-align: center;
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.4);
            position: relative;
            overflow: hidden;
        }
        
        .header::after {
            content: '🌈✨🎨';
            position: absolute;
            top: 10px;
            right: 30px;
            font-size: 2em;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .content {
            padding: 40px;
        }
        
        .page {
            margin-bottom: 60px;
            padding: 30px;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            border: 3px solid transparent;
            background-clip: padding-box;
            box-shadow: 0 15px 35px rgba(255, 107, 107, 0.2);
            position: relative;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .page::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            z-index: -1;
            margin: -3px;
            border-radius: 15px;
        }
        
        .page:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 25px 50px rgba(255, 107, 107, 0.3);
        }
        
        .page-number {
            position: absolute;
            top: -15px;
            right: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            color: white;
            padding: 10px 18px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 700;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            animation: pulse 2s infinite;
            border: 2px solid white;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        h1, h2, h3 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .feature-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #ff6b6b;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .feature-box:hover {
            transform: translateX(10px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }
        
        .emoji-text {
            font-size: 1.2em;
            margin: 15px 0;
        }
        
        .color-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .color-card {
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .color-card:hover {
            transform: scale(1.05) rotate(2deg);
        }
        
        .card1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .card3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .card4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .card5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .card6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌈 Super Colorful PDF to HTML</h1>
            <p>Your documents now have AMAZING colors and animations!</p>
        </div>
        <div class="content">
            <div class="page">
                <div class="page-number">Demo</div>
                
                <h2>🎨 Colorful Features Added!</h2>
                
                <div class="feature-box">
                    <h3>🌈 Animated Backgrounds</h3>
                    <p class="emoji-text">✨ Watch the background colors change automatically!</p>
                    <p>Your PDF conversions now have beautiful, animated gradient backgrounds that shift through rainbow colors.</p>
                </div>
                
                <div class="feature-box">
                    <h3>🎯 Pulsing Page Numbers</h3>
                    <p class="emoji-text">💫 Page numbers that pulse and glow!</p>
                    <p>Each page number is now a colorful badge that pulses with animation.</p>
                </div>
                
                <div class="feature-box">
                    <h3>🎪 Hover Effects</h3>
                    <p class="emoji-text">🚀 Interactive elements that respond to your mouse!</p>
                    <p>Hover over any element to see beautiful animations and transformations.</p>
                </div>
                
                <h2>🎨 Color Palette Demo</h2>
                <div class="color-demo">
                    <div class="color-card card1">Purple Magic</div>
                    <div class="color-card card2">Pink Power</div>
                    <div class="color-card card3">Blue Bliss</div>
                    <div class="color-card card4">Green Glow</div>
                    <div class="color-card card5">Sunset Vibes</div>
                    <div class="color-card card6">Pastel Dreams</div>
                </div>
                
                <div class="feature-box">
                    <h3>🎭 What's New?</h3>
                    <p class="emoji-text">🌟 Every conversion method now has vibrant colors:</p>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li><strong>PDFPlumber:</strong> Rainbow gradients with shimmer effects</li>
                        <li><strong>PyMuPDF:</strong> Colorful backgrounds with bouncing emojis</li>
                        <li><strong>PDFMiner:</strong> Sparkling headers with color waves</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 40px; font-size: 1.5em;">
                    🎉 Your PDF to HTML conversions are now SUPER COLORFUL! 🎉
                </div>
            </div>
        </div>
    </div>
</body>
</html>
