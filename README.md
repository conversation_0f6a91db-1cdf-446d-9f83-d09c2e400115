# 🔄 Universal File Converter

A modern, user-friendly GUI application for converting between HTML, PDF, and Markdown formats with beautiful CSS themes.

![Universal File Converter](https://img.shields.io/badge/Python-3.7+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20macOS%20%7C%20Linux-lightgrey.svg)

## ✨ Features

### 🎨 **Beautiful GUI with Preview**
- Modern tkinter interface with tabbed layout
- **Live preview pane** - see your conversions in real-time
- Progress bars and status updates
- Responsive design

### 📄 **HTML → PDF Conversion**
- Chrome-based headless conversion
- Multiple page sizes (A4, Letter, Legal, A3, A5)
- Customizable margins (None, Minimal, Normal, Wide)
- High-quality output

### 📋 **PDF → HTML Conversion**
- **PDFPlumber**: Simple text extraction with basic formatting
- **PyMuPDF**: Better layout and formatting preservation  
- **PDFMiner**: Detailed control over extraction process
- Preserves text styling and structure

### 📝 **Markdown → HTML Conversion**
- **5 stunning CSS themes** included
- Support for tables, code blocks, and TOC
- Responsive design for all devices
- Print-optimized output

## 🎨 Available CSS Themes

| Theme | Description | Best For |
|-------|-------------|----------|
| **Modern Professional** | Clean, corporate design with gradients | Business documents, reports, proposals |
| **Academic Paper** | Traditional scholarly layout with serif fonts | Research papers, theses, academic articles |
| **Blog Article** | Medium.com inspired reading experience | Blog posts, articles, tutorials |
| **GitHub README** | Familiar developer-friendly design | Documentation, technical guides, API docs |
| **Presentation Slides** | Full-screen slide deck with navigation | Presentations, talks, workshops |

## 🚀 Quick Start

### Option 1: Easy Launcher (Recommended)
```bash
python run_converter.py
```
The launcher will automatically check and install missing dependencies.

### Option 2: Manual Setup
1. **Install dependencies:**
   ```bash
   pip install markdown pdfplumber PyMuPDF pdfminer.six
   ```

2. **Run the application:**
   ```bash
   python unified_converter_gui.py
   ```

## 📖 How to Use

### 1. **HTML → PDF**
- Click the "📄 HTML → PDF" tab
- Browse and select your HTML file
- Choose page size and margins
- Click "🔄 Convert to PDF"
- Preview and save your PDF

### 2. **PDF → HTML**
- Click the "📋 PDF → HTML" tab  
- Browse and select your PDF file
- Choose conversion method:
  - **PDFPlumber**: Fast, simple text extraction
  - **PyMuPDF**: Better formatting preservation
  - **PDFMiner**: Most detailed control
- Click "🔄 Convert to HTML"
- Preview the result in the preview pane

### 3. **Markdown → HTML**
- Click the "📝 Markdown → HTML" tab
- Browse and select your Markdown file
- **Choose a CSS theme** from the dropdown
- Click "Preview Theme" to see theme details
- Click "🔄 Convert to HTML"
- **Live preview** shows your styled document
- Click "🌐 Open in Browser" for full preview

## 🎯 Preview Features

- **Live Preview Pane**: See your content as you work
- **Theme Preview**: Preview CSS themes before converting
- **Browser Integration**: Open results in your default browser
- **Auto-refresh**: Preview updates when you change themes

## 📁 File Structure

```
HTML-PDF-FILE-CONVERSION-TOOL/
├── unified_converter_gui.py      # Main GUI application
├── run_converter.py              # Easy launcher with dependency checking
├── htmltopdf.py                  # Original HTML→PDF converter
├── pdftohtml.py                  # Original PDF→HTML converter
├── demo_sample.md                # Sample Markdown file for testing
├── css_themes/                   # CSS theme files
│   ├── theme_config.py           # Theme configuration
│   ├── modern_professional.css   # Modern business theme
│   ├── academic_paper.css        # Academic/scholarly theme
│   ├── blog_article.css          # Blog/article theme
│   ├── github_readme.css         # GitHub-style theme
│   ├── presentation_slides.css   # Presentation/slides theme
│   └── sample_template.html      # HTML template
└── README.md                     # This file
```

## 🔧 Dependencies

- **Python 3.7+**
- **tkinter** (usually included with Python)
- **markdown** - For Markdown→HTML conversion
- **pdfplumber** - For PDF text extraction
- **PyMuPDF (fitz)** - For advanced PDF processing
- **pdfminer.six** - For detailed PDF analysis
- **Google Chrome** - For HTML→PDF conversion (auto-detected)

## 🎨 Customizing Themes

You can easily add your own CSS themes:

1. Create a new CSS file in the `css_themes/` directory
2. Add your theme to `theme_config.py`:
   ```python
   "your_theme_name": {
       "name": "Your Theme Name",
       "description": "Your theme description",
       "css_file": "your_theme.css",
       # ... other properties
   }
   ```

## 🐛 Troubleshooting

### Chrome Not Found
- Install Google Chrome browser
- The app auto-detects Chrome in standard locations
- For custom installations, update the Chrome paths in the code

### Missing Dependencies
- Run `python run_converter.py` for automatic dependency installation
- Or install manually: `pip install markdown pdfplumber PyMuPDF pdfminer.six`

### Preview Not Working
- Ensure your file is valid HTML/Markdown
- Check that CSS themes are in the `css_themes/` directory
- Try refreshing the preview with the "🔄 Refresh" button

## 🤝 Contributing

Feel free to contribute by:
- Adding new CSS themes
- Improving the GUI design
- Adding new conversion formats
- Fixing bugs or improving performance

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with Python and tkinter
- CSS themes inspired by popular design systems
- PDF processing powered by multiple excellent libraries

---

**Ready to convert your files with style?** 🚀

Try the demo: `python run_converter.py` and convert the included `demo_sample.md` file!
