/* Academic Paper Theme - Traditional, scholarly design */

@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Source+Code+Pro:wght@400;500&display=swap');

:root {
  --primary-color: #1a365d;
  --secondary-color: #4a5568;
  --accent-color: #2b6cb0;
  --background-color: #ffffff;
  --paper-color: #fefefe;
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --border-color: #e2e8f0;
  --citation-color: #2b6cb0;
  --footnote-color: #718096;
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Crimson Text', 'Times New Roman', serif;
  line-height: 1.8;
  color: var(--text-primary);
  background-color: var(--background-color);
  margin: 0;
  padding: 2rem;
  font-size: 18px;
  text-align: justify;
  hyphens: auto;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: var(--paper-color);
  padding: 4rem;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  min-height: calc(100vh - 4rem);
}

/* Academic Header */
.paper-header {
  text-align: center;
  margin-bottom: 3rem;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 2rem;
}

.paper-title {
  font-size: 2.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.paper-subtitle {
  font-size: 1.3rem;
  color: var(--secondary-color);
  font-style: italic;
  margin-bottom: 1.5rem;
}

.paper-authors {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.paper-affiliation {
  font-size: 1rem;
  color: var(--secondary-color);
  font-style: italic;
}

.paper-date {
  font-size: 0.95rem;
  color: var(--footnote-color);
  margin-top: 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.4;
  margin: 2.5rem 0 1rem 0;
  color: var(--primary-color);
}

h1 {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 2rem;
}

h2 {
  font-size: 1.8rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
  margin-top: 3rem;
}

h3 {
  font-size: 1.4rem;
  margin-top: 2rem;
}

h4 {
  font-size: 1.2rem;
  font-style: italic;
}

h5, h6 {
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--secondary-color);
}

p {
  margin: 1.2rem 0;
  text-indent: 1.5rem;
}

p:first-of-type,
h1 + p, h2 + p, h3 + p, h4 + p, h5 + p, h6 + p {
  text-indent: 0;
}

/* Abstract */
.abstract {
  background: #f7fafc;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 2rem;
  margin: 2rem 0;
  font-style: italic;
}

.abstract h3 {
  margin-top: 0;
  text-align: center;
  font-style: normal;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 1rem;
}

/* Links and Citations */
a {
  color: var(--citation-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

.citation {
  color: var(--citation-color);
  font-weight: 500;
}

/* Footnotes */
.footnote {
  font-size: 0.9rem;
  color: var(--footnote-color);
  border-top: 1px solid var(--border-color);
  margin-top: 3rem;
  padding-top: 1rem;
}

.footnote-ref {
  vertical-align: super;
  font-size: 0.8rem;
  color: var(--citation-color);
}

/* Lists */
ul, ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

li {
  margin: 0.8rem 0;
}

/* Code */
code {
  font-family: 'Source Code Pro', 'Courier New', monospace;
  background: #f7fafc;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.9em;
  color: var(--accent-color);
}

pre {
  background: #f7fafc;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 1.5rem;
  overflow-x: auto;
  margin: 2rem 0;
  font-size: 0.9rem;
}

pre code {
  background: none;
  padding: 0;
  color: var(--text-primary);
}

/* Blockquotes */
blockquote {
  border-left: 3px solid var(--accent-color);
  margin: 2rem 0;
  padding: 1rem 2rem;
  background: #f7fafc;
  font-style: italic;
  position: relative;
}

blockquote p {
  text-indent: 0;
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  font-size: 0.95rem;
}

th, td {
  padding: 0.8rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background: #f7fafc;
  font-weight: 600;
  color: var(--primary-color);
  border-bottom: 2px solid var(--border-color);
}

caption {
  caption-side: bottom;
  margin-top: 0.5rem;
  font-style: italic;
  color: var(--secondary-color);
  text-align: center;
}

/* Figures */
figure {
  margin: 2rem 0;
  text-align: center;
}

figcaption {
  font-style: italic;
  color: var(--secondary-color);
  margin-top: 0.5rem;
  font-size: 0.95rem;
}

img {
  max-width: 100%;
  height: auto;
  border: 1px solid var(--border-color);
}

/* Table of Contents */
.toc {
  background: #f7fafc;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 2rem;
  margin: 2rem 0;
}

.toc h3 {
  margin-top: 0;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-size: 1rem;
}

.toc ul {
  list-style: none;
  padding-left: 0;
}

.toc li {
  margin: 0.5rem 0;
}

.toc a {
  text-decoration: none;
  color: var(--text-primary);
}

.toc a:hover {
  color: var(--citation-color);
}

/* Page Numbers for Print */
@page {
  margin: 1in;
  @bottom-center {
    content: counter(page);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 1rem;
    font-size: 16px;
  }
  
  .container {
    padding: 2rem;
  }
  
  h1 {
    font-size: 1.8rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  p {
    text-indent: 1rem;
  }
}

/* Print Styles */
@media print {
  body {
    background: white;
    padding: 0;
  }
  
  .container {
    box-shadow: none;
    padding: 0;
    max-width: none;
  }
  
  h1, h2 {
    page-break-after: avoid;
  }
  
  blockquote, table, figure {
    page-break-inside: avoid;
  }
}
