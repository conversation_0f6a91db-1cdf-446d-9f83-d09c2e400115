"""
CSS Theme Configuration for Markdown to HTML Converter
Defines available themes and their properties
"""

import os
from pathlib import Path

# Get the directory where this file is located
THEMES_DIR = Path(__file__).parent

# Theme definitions
THEMES = {
    "modern_professional": {
        "name": "Modern Professional",
        "description": "Clean, corporate-friendly design with gradients and modern typography",
        "css_file": "modern_professional.css",
        "preview_image": "previews/modern_professional.png",
        "best_for": ["Business documents", "Reports", "Proposals", "Documentation"],
        "features": [
            "Inter font family",
            "Gradient headers",
            "Card-based layout",
            "Syntax highlighting",
            "Responsive design"
        ],
        "template_vars": {
            "container_class": "container",
            "supports_toc": True,
            "supports_math": True
        }
    },
    
    "academic_paper": {
        "name": "Academic Paper",
        "description": "Traditional scholarly design with serif fonts and formal layout",
        "css_file": "academic_paper.css",
        "preview_image": "previews/academic_paper.png",
        "best_for": ["Research papers", "Theses", "Academic articles", "Formal documents"],
        "features": [
            "Crimson Text serif font",
            "Citation styling",
            "Footnotes support",
            "Abstract section",
            "Bibliography formatting"
        ],
        "template_vars": {
            "container_class": "container",
            "supports_toc": True,
            "supports_math": True,
            "supports_citations": True,
            "supports_footnotes": True
        }
    },
    
    "blog_article": {
        "name": "Blog Article",
        "description": "Medium.com inspired design for engaging reading experience",
        "css_file": "blog_article.css",
        "preview_image": "previews/blog_article.png",
        "best_for": ["Blog posts", "Articles", "Tutorials", "Personal writing"],
        "features": [
            "Charter serif font",
            "Reading time estimate",
            "Social sharing buttons",
            "Drop cap styling",
            "Highlight effects"
        ],
        "template_vars": {
            "container_class": "container",
            "supports_author": True,
            "supports_social": True,
            "supports_reading_time": True
        }
    },
    
    "github_readme": {
        "name": "GitHub README",
        "description": "Familiar developer-friendly design matching GitHub's style",
        "css_file": "github_readme.css",
        "preview_image": "previews/github_readme.png",
        "best_for": ["README files", "Documentation", "Technical guides", "API docs"],
        "features": [
            "Inter font family",
            "GitHub color scheme",
            "Badge support",
            "Code highlighting",
            "Task list styling"
        ],
        "template_vars": {
            "container_class": "container",
            "supports_badges": True,
            "supports_alerts": True,
            "supports_task_lists": True
        }
    },
    
    "presentation_slides": {
        "name": "Presentation Slides",
        "description": "Full-screen slide deck with navigation and animations",
        "css_file": "presentation_slides.css",
        "preview_image": "previews/presentation_slides.png",
        "best_for": ["Presentations", "Slide decks", "Talks", "Workshops"],
        "features": [
            "Poppins font family",
            "Full-screen slides",
            "Slide navigation",
            "Progress bar",
            "Keyboard controls"
        ],
        "template_vars": {
            "container_class": "presentation-container",
            "supports_slides": True,
            "supports_navigation": True,
            "supports_animations": True
        }
    }
}

def get_theme_css_path(theme_name):
    """Get the full path to a theme's CSS file"""
    if theme_name not in THEMES:
        raise ValueError(f"Theme '{theme_name}' not found")
    
    css_file = THEMES[theme_name]["css_file"]
    return THEMES_DIR / css_file

def get_theme_css_content(theme_name):
    """Get the CSS content for a theme"""
    css_path = get_theme_css_path(theme_name)
    
    if not css_path.exists():
        raise FileNotFoundError(f"CSS file not found: {css_path}")
    
    with open(css_path, 'r', encoding='utf-8') as f:
        return f.read()

def get_available_themes():
    """Get list of available theme names"""
    return list(THEMES.keys())

def get_theme_info(theme_name):
    """Get detailed information about a theme"""
    if theme_name not in THEMES:
        raise ValueError(f"Theme '{theme_name}' not found")
    
    return THEMES[theme_name].copy()

def get_all_themes_info():
    """Get information about all available themes"""
    return {name: info.copy() for name, info in THEMES.items()}

# HTML template for embedding CSS
HTML_TEMPLATE = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
{css_content}
    </style>
</head>
<body>
    <div class="{container_class}">
{content}
    </div>
    
    {additional_scripts}
</body>
</html>'''

# Additional scripts for specific themes
THEME_SCRIPTS = {
    "presentation_slides": '''
    <script>
        // Presentation navigation
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        function updateSlideCounter() {
            const counter = document.querySelector('.slide-counter');
            if (counter) {
                counter.textContent = `${currentSlide + 1} / ${totalSlides}`;
            }
        }
        
        function updateProgressBar() {
            const progressBar = document.querySelector('.progress-bar');
            if (progressBar) {
                const progress = ((currentSlide + 1) / totalSlides) * 100;
                progressBar.style.width = `${progress}%`;
            }
        }
        
        function goToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                currentSlide = index;
                slides[currentSlide].scrollIntoView({ behavior: 'smooth' });
                updateSlideCounter();
                updateProgressBar();
            }
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowDown' || e.key === 'ArrowRight' || e.key === ' ') {
                e.preventDefault();
                goToSlide(currentSlide + 1);
            } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
                e.preventDefault();
                goToSlide(currentSlide - 1);
            } else if (e.key === 'Home') {
                e.preventDefault();
                goToSlide(0);
            } else if (e.key === 'End') {
                e.preventDefault();
                goToSlide(totalSlides - 1);
            }
        });
        
        // Initialize
        updateSlideCounter();
        updateProgressBar();
    </script>
    ''',
    
    "blog_article": '''
    <script>
        // Reading time calculation
        function calculateReadingTime() {
            const text = document.querySelector('.container').textContent;
            const wordsPerMinute = 200;
            const words = text.trim().split(/\s+/).length;
            const readingTime = Math.ceil(words / wordsPerMinute);
            
            const readingTimeElement = document.querySelector('.reading-time');
            if (readingTimeElement) {
                readingTimeElement.textContent = `${readingTime} min read`;
            }
        }
        
        // Initialize
        calculateReadingTime();
    </script>
    '''
}

def create_html_with_theme(content, theme_name, title="Document", **kwargs):
    """
    Create complete HTML document with theme applied
    
    Args:
        content: The HTML content to wrap
        theme_name: Name of the theme to apply
        title: Document title
        **kwargs: Additional template variables
    
    Returns:
        Complete HTML document as string
    """
    if theme_name not in THEMES:
        raise ValueError(f"Theme '{theme_name}' not found")
    
    theme_info = THEMES[theme_name]
    css_content = get_theme_css_content(theme_name)
    container_class = theme_info["template_vars"].get("container_class", "container")
    
    # Get additional scripts for this theme
    additional_scripts = THEME_SCRIPTS.get(theme_name, "")
    
    return HTML_TEMPLATE.format(
        title=title,
        css_content=css_content,
        content=content,
        container_class=container_class,
        additional_scripts=additional_scripts
    )

# Export main functions
__all__ = [
    'THEMES',
    'get_theme_css_path',
    'get_theme_css_content', 
    'get_available_themes',
    'get_theme_info',
    'get_all_themes_info',
    'create_html_with_theme'
]
