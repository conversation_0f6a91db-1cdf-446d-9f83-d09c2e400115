/* Modern Professional Theme - Clean, corporate-friendly design */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

:root {
  --primary-color: #2563eb;
  --secondary-color: #64748b;
  --accent-color: #0ea5e9;
  --background-color: #ffffff;
  --surface-color: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --radius: 8px;
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--background-color);
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin: 2rem 0 1rem 0;
  color: var(--text-primary);
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 2rem;
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 0.5rem;
  margin-top: 3rem;
}

h3 {
  font-size: 1.5rem;
  color: var(--primary-color);
}

h4 {
  font-size: 1.25rem;
}

h5, h6 {
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--secondary-color);
}

p {
  margin: 1rem 0;
  color: var(--text-primary);
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

a:hover {
  border-bottom-color: var(--primary-color);
  color: var(--accent-color);
}

/* Lists */
ul, ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

li {
  margin: 0.5rem 0;
}

ul li::marker {
  color: var(--primary-color);
}

/* Code */
code {
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  background: var(--surface-color);
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.9em;
  color: var(--accent-color);
  border: 1px solid var(--border-color);
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: 1.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
  box-shadow: var(--shadow-sm);
}

pre code {
  background: none;
  border: none;
  padding: 0;
  color: var(--text-primary);
}

/* Blockquotes */
blockquote {
  border-left: 4px solid var(--primary-color);
  margin: 2rem 0;
  padding: 1rem 2rem;
  background: var(--surface-color);
  border-radius: 0 var(--radius) var(--radius) 0;
  font-style: italic;
  position: relative;
}

blockquote::before {
  content: '"';
  font-size: 4rem;
  color: var(--primary-color);
  position: absolute;
  top: -1rem;
  left: 1rem;
  opacity: 0.3;
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: var(--background-color);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background: var(--surface-color);
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.9rem;
}

tr:hover {
  background: var(--surface-color);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius);
  box-shadow: var(--shadow-md);
  margin: 1rem 0;
}

/* Horizontal Rule */
hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
  margin: 3rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  table {
    font-size: 0.9rem;
  }
  
  th, td {
    padding: 0.5rem;
  }
}

/* Print Styles */
@media print {
  .container {
    max-width: none;
    padding: 0;
  }
  
  h1 {
    color: var(--text-primary) !important;
    -webkit-text-fill-color: var(--text-primary) !important;
  }
  
  a {
    color: var(--text-primary);
    border-bottom: none;
  }
  
  blockquote {
    break-inside: avoid;
  }
  
  table {
    break-inside: avoid;
  }
}
