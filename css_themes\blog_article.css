/* Blog Article Theme - Medium.com inspired, modern reading experience */

@import url('https://fonts.googleapis.com/css2?family=Charter:ital,wght@0,400;0,700;1,400&family=SF+Mono:wght@400;500&display=swap');

:root {
  --primary-color: #1a8917;
  --secondary-color: #6b7280;
  --accent-color: #059669;
  --background-color: #ffffff;
  --surface-color: #f9fafb;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-light: #9ca3af;
  --border-color: #e5e7eb;
  --highlight-color: #fef3c7;
  --link-color: #1a8917;
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Charter', 'Georgia', serif;
  line-height: 1.7;
  color: var(--text-primary);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
  font-size: 20px;
  -webkit-font-smoothing: antialiased;
}

.container {
  max-width: 680px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
}

/* Article Header */
.article-header {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--border-color);
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.article-info {
  flex: 1;
}

.author-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.2rem;
}

.publish-date {
  color: var(--text-light);
}

.reading-time {
  background: var(--surface-color);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.3;
  margin: 2rem 0 1rem 0;
  color: var(--text-primary);
}

h1 {
  font-size: 2.8rem;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

h2 {
  font-size: 2rem;
  margin-top: 3rem;
  position: relative;
}

h2::before {
  content: '';
  position: absolute;
  left: -2rem;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 2rem;
  background: var(--primary-color);
  border-radius: 2px;
}

h3 {
  font-size: 1.5rem;
  color: var(--primary-color);
}

h4 {
  font-size: 1.25rem;
}

h5, h6 {
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--text-secondary);
}

p {
  margin: 1.5rem 0;
  color: var(--text-primary);
}

/* Drop Cap */
.drop-cap::first-letter {
  float: left;
  font-size: 4rem;
  line-height: 3rem;
  padding-right: 0.5rem;
  margin-top: 0.2rem;
  color: var(--primary-color);
  font-weight: 700;
}

/* Links */
a {
  color: var(--link-color);
  text-decoration: underline;
  text-decoration-color: transparent;
  transition: all 0.2s ease;
}

a:hover {
  text-decoration-color: var(--link-color);
}

/* Emphasis */
em {
  font-style: italic;
  background: linear-gradient(120deg, var(--highlight-color) 0%, var(--highlight-color) 100%);
  background-repeat: no-repeat;
  background-size: 100% 0.3em;
  background-position: 0 88%;
}

strong {
  font-weight: 700;
  color: var(--text-primary);
}

/* Lists */
ul, ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

li {
  margin: 1rem 0;
}

ul li::marker {
  color: var(--primary-color);
}

/* Code */
code {
  font-family: 'SF Mono', 'Monaco', monospace;
  background: var(--surface-color);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85em;
  color: var(--accent-color);
  border: 1px solid var(--border-color);
}

pre {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 2rem;
  overflow-x: auto;
  margin: 2rem 0;
  position: relative;
}

pre::before {
  content: '';
  position: absolute;
  top: 1rem;
  left: 1rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff5f56;
  box-shadow: 20px 0 #ffbd2e, 40px 0 #27ca3f;
}

pre code {
  background: none;
  border: none;
  padding: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

/* Blockquotes */
blockquote {
  border-left: 4px solid var(--primary-color);
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  background: var(--surface-color);
  border-radius: 0 8px 8px 0;
  font-size: 1.1em;
  font-style: italic;
  position: relative;
}

blockquote::before {
  content: '"';
  font-size: 6rem;
  color: var(--primary-color);
  position: absolute;
  top: -2rem;
  left: 1rem;
  opacity: 0.2;
  font-family: serif;
}

blockquote p {
  margin: 0;
}

blockquote cite {
  display: block;
  margin-top: 1rem;
  font-size: 0.9em;
  color: var(--text-secondary);
  font-style: normal;
}

blockquote cite::before {
  content: '— ';
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: var(--background-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background: var(--surface-color);
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

tr:hover {
  background: var(--surface-color);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 2rem 0;
}

figure {
  margin: 2rem 0;
  text-align: center;
}

figcaption {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
  font-style: italic;
}

/* Horizontal Rule */
hr {
  border: none;
  height: 1px;
  background: var(--border-color);
  margin: 3rem 0;
  position: relative;
}

hr::before {
  content: '***';
  position: absolute;
  top: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  background: var(--background-color);
  padding: 0 1rem;
  color: var(--text-light);
  letter-spacing: 0.5rem;
}

/* Call-to-Action */
.cta {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  margin: 3rem 0;
}

.cta h3 {
  color: white;
  margin-top: 0;
}

.cta-button {
  display: inline-block;
  background: white;
  color: var(--primary-color);
  padding: 0.8rem 2rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  margin-top: 1rem;
  transition: transform 0.2s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
  color: var(--primary-color);
}

/* Social Share */
.social-share {
  display: flex;
  gap: 1rem;
  margin: 3rem 0;
  padding: 1rem 0;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.share-button {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background: var(--surface-color);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.share-button:hover {
  background: var(--primary-color);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    font-size: 18px;
  }
  
  .container {
    padding: 1rem;
  }
  
  h1 {
    font-size: 2.2rem;
  }
  
  h2 {
    font-size: 1.6rem;
  }
  
  h2::before {
    left: -1rem;
  }
  
  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  pre {
    padding: 1rem;
  }
}

/* Print Styles */
@media print {
  .container {
    max-width: none;
    padding: 0;
  }
  
  .social-share,
  .cta {
    display: none;
  }
  
  a {
    color: var(--text-primary);
    text-decoration: none;
  }
}
